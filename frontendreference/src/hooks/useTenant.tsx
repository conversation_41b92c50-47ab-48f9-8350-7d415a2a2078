import {
  createContext,
  useContext,
  useState,
  useEffect,
  type ReactNode,
} from "react";
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Doc, Id } from "../../convex/_generated/dataModel";

interface TenantContextType {
  tenants: Doc<"tenants">[] | undefined;
  selectedTenant: Doc<"tenants"> | null;
  tenantId: Id<"tenants"> | null;
  setSelectedTenantId: (tenantId: Id<"tenants">) => void;
  isLoading: boolean;
}

const TenantContext = createContext<TenantContextType | undefined>(undefined);

export function TenantProvider({ children }: { children: ReactNode }) {
  const tenants = useQuery(api.tenants.list);
  const [selectedTenantId, setSelectedTenantId] = useState<
    Id<"tenants"> | ""
  >(() => {
    if (typeof window !== "undefined") {
      const v1 = localStorage.getItem("currentTenantId") as Id<"tenants"> | null;
      const v2 = localStorage.getItem("selectedTenantId") as Id<"tenants"> | null;
      return v1 || v2 || "";
    }
    return "";
  });

  useEffect(() => {
    if (typeof window !== "undefined") {
      // maintain both keys for compatibility
      if (selectedTenantId) {
        localStorage.setItem("currentTenantId", selectedTenantId);
        localStorage.setItem("selectedTenantId", selectedTenantId);
      }
    }
  }, [selectedTenantId]);

  const selectedTenant =
    tenants?.find((t) => t._id === selectedTenantId) ?? null;
  const isLoading = tenants === undefined;

  const value = {
    tenants,
    selectedTenant,
    tenantId: selectedTenant?._id ?? null,
    setSelectedTenantId: setSelectedTenantId as (
      tenantId: Id<"tenants">
    ) => void,
    isLoading,
  };

  return (
    <TenantContext.Provider value={value}>{children}</TenantContext.Provider>
  );
}

export function useTenant() {
  const context = useContext(TenantContext);
  if (context === undefined) {
    throw new Error("useTenant must be used within a TenantProvider");
  }
  return context;
}
