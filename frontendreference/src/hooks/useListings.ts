import { useState } from "react";
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";

export function useListings(tenantId: Id<"tenants"> | null) {
  const [selectedCategoryId, setSelectedCategoryId] = useState<
    Id<"categories"> | undefined
  >(undefined);

  const listings = useQuery(
    api.listings.get,
    tenantId
      ? { tenantId, categoryId: selectedCategoryId }
      : "skip"
  );

  const categories = useQuery(
    api.listings.getCategories,
    tenantId ? { tenantId } : "skip"
  );

  const isLoading = listings === undefined || categories === undefined;

  return {
    listings,
    categories,
    isLoading,
    selectedCategoryId,
    setSelectedCategoryId,
  };
}
