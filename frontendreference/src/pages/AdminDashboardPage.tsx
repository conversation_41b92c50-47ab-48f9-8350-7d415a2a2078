import { Routes, Route } from "react-router-dom";
import { AdminSidebar } from "../components/admin/AdminSidebar";
import { StatsPanel } from "../components/admin/StatsPanel";
import { PendingActions } from "../components/admin/PendingActions";
import { PendingReviews } from "../components/admin/PendingReviews";
import { AdminSearch } from "../components/admin/AdminSearch";
import { ManageListings } from "../components/admin/ManageListings";
import { UserManagement } from "../components/admin/UserManagement";
import { SeedButton } from "../components/admin/SeedButton";
import { CsvUpload } from "../components/admin/CsvUpload";

export function AdminScreen() {
  return (
    <div className="flex gap-8">
      <AdminSidebar />
      <div className="flex-1">
        <Routes>
          <Route path="/" element={
            <>
              <StatsPanel />
              <PendingActions />
              <SeedButton />
              <CsvUpload />
            </>
          } />
          <Route path="/listings" element={<ManageListings />} />
          <Route path="/reviews" element={<PendingReviews />} />
          <Route path="/search" element={<AdminSearch />} />
          <Route path="/users" element={<UserManagement />} />
        </Routes>
      </div>
    </div>
  );
}
