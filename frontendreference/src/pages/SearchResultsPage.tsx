import { useQuery } from "convex/react";
import { useLocation, useNavigate } from "react-router-dom";
import { api } from "../../convex/_generated/api";
import { useTenant } from "../hooks/useTenant";
import { ListingCard } from "../components/listings/ListingCard";
import { SearchBar } from "../components/home/<USER>";

export function SearchResultsPage() {
  const location = useLocation();
  const navigate = useNavigate();
  const { tenantId } = useTenant();
  
  // Extract search term from URL query parameter
  const urlParams = new URLSearchParams(location.search);
  const searchTerm = urlParams.get("q") || "";
  
  // Perform search query
  const searchResults = useQuery(
    api.listings.search,
    tenantId && searchTerm ? { tenantId, searchTerm } : "skip"
  );

  const handleSearch = (term: string) => {
    navigate(`/search?q=${encodeURIComponent(term)}`);
  };

  return (
    <div className="space-y-8">
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h1 className="text-2xl font-bold text-primary mb-4">Search Results</h1>
        <SearchBar onSearch={handleSearch} />
        {searchTerm && (
          <p className="mt-4 text-gray-600">
            Search results for: <span className="font-semibold">{searchTerm}</span>
          </p>
        )}
      </div>

      {searchResults === undefined ? (
        <div className="text-center py-8">Loading...</div>
      ) : searchResults.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500">
            No results found for "{searchTerm}". Try a different search term.
          </p>
        </div>
      ) : (
        <div>
          <h2 className="text-xl font-semibold mb-4">
            Found {searchResults.length} result{searchResults.length !== 1 ? "s" : ""}
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {searchResults.map((listing) => (
              <ListingCard key={listing._id} listing={listing} />
            ))}
          </div>
        </div>
      )}
    </div>
  );
}