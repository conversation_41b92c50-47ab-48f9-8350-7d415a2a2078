// Enhanced Home Page
import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { SearchBar } from "../components/SearchBar";

export function Home() {
  const [count, setCount] = useState(0);

  return (
    <div className="max-w-4xl mx-auto">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold text-primary mb-4">Welcome to Hyperlocal BD</h1>
        <p className="text-xl text-gray-600">
          Discover local businesses and services in your area
        </p>
      </div>

      <SearchBar />

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-2xl font-bold mb-4">Find Local Businesses</h2>
          <p className="mb-4">
            Browse through our curated list of local businesses and services in your area.
          </p>
          <Link to="/businesses">
            <button className="px-4 py-2 bg-primary text-white rounded hover:bg-primary-hover">
              Browse Listings
            </button>
          </Link>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-2xl font-bold mb-4">Submit Your Business</h2>
          <p className="mb-4">
            Are you a local business owner? Add your business to our directory.
          </p>
          <Link to="/submit-business">
            <button className="px-4 py-2 bg-primary text-white rounded hover:bg-primary-hover">
              Submit Business
            </button>
          </Link>
        </div>
      </div>

      <div className="bg-white p-6 rounded-lg shadow-md mb-8">
        <h2 className="text-2xl font-bold mb-4">Explore Categories</h2>
        <p className="mb-4">
          Browse businesses by category to find exactly what you're looking for.
        </p>
        <Link to="/categories">
          <button className="px-4 py-2 bg-primary text-white rounded hover:bg-primary-hover">
            View All Categories
          </button>
        </Link>
      </div>

      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-2xl font-bold mb-4">Interactive Demo</h2>
        <p className="mb-4">Count: {count}</p>
        <div className="flex space-x-2">
          <button 
            className="px-4 py-2 bg-primary text-white rounded hover:bg-primary-hover"
            onClick={() => setCount(count + 1)}
          >
            Increment
          </button>
          <button 
            className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
            onClick={() => setCount(0)}
          >
            Reset
          </button>
          <Link to="/about">
            <button className="px-4 py-2 bg-primary text-white rounded hover:bg-primary-hover">
              Learn More
            </button>
          </Link>
        </div>
      </div>
    </div>
  );
}