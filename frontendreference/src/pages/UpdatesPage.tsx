// src/pages/UpdatesPage.tsx
// Updates page

import { TodayAlerts } from "../components/home/<USER>";
import { LocalNews } from "../components/home/<USER>";

export function UpdatesPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-3">
          <h1 className="text-xl font-bold text-gray-900">Community Updates</h1>
        </div>
      </header>

      <main className="container mx-auto px-4 py-6">
        {/* Today's Alerts & Updates */}
        <section className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-bold text-gray-900">🔥 TODAY'S ALERTS & UPDATES</h2>
          </div>
          <TodayAlerts />
        </section>

        {/* Local News Headlines */}
        <section className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-bold text-gray-900">📰 LOCAL NEWS HEADLINES</h2>
          </div>
          <LocalNews />
        </section>
      </main>

      {/* Bottom Navigation */}
      <nav className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200">
        <div className="grid grid-cols-4">
          <a 
            href="/" 
            className="flex flex-col items-center justify-center py-2 text-gray-500"
          >
            <span>🏠</span>
            <span className="text-xs">Home</span>
          </a>
          <a 
            href="/categories" 
            className="flex flex-col items-center justify-center py-2 text-gray-500"
          >
            <span>📂</span>
            <span className="text-xs">Categories</span>
          </a>
          <a 
            href="/updates" 
            className="flex flex-col items-center justify-center py-2 text-primary"
          >
            <span>🔔</span>
            <span className="text-xs">Updates</span>
          </a>
          <a 
            href="/profile" 
            className="flex flex-col items-center justify-center py-2 text-gray-500"
          >
            <span>👤</span>
            <span className="text-xs">Profile</span>
          </a>
        </div>
      </nav>
    </div>
  );
}