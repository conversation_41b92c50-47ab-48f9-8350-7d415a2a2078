// Dashboard Page (Protected)
import { useAuth } from "../contexts/AuthContext";

export function Dashboard() {
  const { logout } = useAuth();
  
  return (
    <div className="max-w-4xl mx-auto">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold text-primary">Dashboard</h1>
        <button
          onClick={logout}
          className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
        >
          Logout
        </button>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-bold mb-2">Your Listings</h2>
          <p className="text-3xl font-bold text-primary">12</p>
          <p className="text-gray-600">Active listings</p>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-bold mb-2">Views</h2>
          <p className="text-3xl font-bold text-primary">1,248</p>
          <p className="text-gray-600">Total views</p>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-bold mb-2">Reviews</h2>
          <p className="text-3xl font-bold text-primary">42</p>
          <p className="text-gray-600">Customer reviews</p>
        </div>
      </div>
      
      <div className="mt-8 bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-2xl font-bold mb-4">Recent Activity</h2>
        <ul className="space-y-3">
          <li className="flex items-center">
            <div className="bg-green-100 p-2 rounded-full mr-3">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            </div>
            <p>
              <span className="font-semibold">New review</span> for "Dhaka Coffee Shop" - 2 hours ago
            </p>
          </li>
          <li className="flex items-center">
            <div className="bg-blue-100 p-2 rounded-full mr-3">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
            </div>
            <p>
              <span className="font-semibold">Listing updated</span> for "Tech Gadgets BD" - 1 day ago
            </p>
          </li>
          <li className="flex items-center">
            <div className="bg-yellow-100 p-2 rounded-full mr-3">
              <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
            </div>
            <p>
              <span className="font-semibold">New message</span> from customer - 2 days ago
            </p>
          </li>
        </ul>
      </div>
    </div>
  );
}