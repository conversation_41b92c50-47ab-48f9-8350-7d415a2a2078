// src/pages/ProfilePage.tsx
// Profile page

export function ProfilePage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-3">
          <h1 className="text-xl font-bold text-gray-900">My Profile</h1>
        </div>
      </header>

      <main className="container mx-auto px-4 py-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center mb-6">
            <div className="bg-gray-200 border-2 border-dashed rounded-xl w-16 h-16" />
            <div className="ml-4">
              <h2 className="text-lg font-bold">User Name</h2>
              <p className="text-gray-600">Ward 3, Alfadanga</p>
            </div>
          </div>
          
          <div className="space-y-4">
            <div className="flex justify-between items-center pb-2 border-b">
              <span>My Listings</span>
              <span className="text-primary">→</span>
            </div>
            <div className="flex justify-between items-center pb-2 border-b">
              <span>My Reviews</span>
              <span className="text-primary">→</span>
            </div>
            <div className="flex justify-between items-center pb-2 border-b">
              <span>Settings</span>
              <span className="text-primary">→</span>
            </div>
            <div className="flex justify-between items-center pb-2 border-b">
              <span>Help & Support</span>
              <span className="text-primary">→</span>
            </div>
          </div>
        </div>
      </main>

      {/* Bottom Navigation */}
      <nav className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200">
        <div className="grid grid-cols-4">
          <a 
            href="/" 
            className="flex flex-col items-center justify-center py-2 text-gray-500"
          >
            <span>🏠</span>
            <span className="text-xs">Home</span>
          </a>
          <a 
            href="/categories" 
            className="flex flex-col items-center justify-center py-2 text-gray-500"
          >
            <span>📂</span>
            <span className="text-xs">Categories</span>
          </a>
          <a 
            href="/updates" 
            className="flex flex-col items-center justify-center py-2 text-gray-500"
          >
            <span>🔔</span>
            <span className="text-xs">Updates</span>
          </a>
          <a 
            href="/profile" 
            className="flex flex-col items-center justify-center py-2 text-primary"
          >
            <span>👤</span>
            <span className="text-xs">Profile</span>
          </a>
        </div>
      </nav>
    </div>
  );
}