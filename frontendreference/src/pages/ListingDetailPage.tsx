import { useParams } from "react-router-dom";
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { MapPin, Phone, Globe, Tag, Star, Navigation, MessageSquare } from "lucide-react";
import { ReviewsSection } from "../components/listings/ReviewsSection";

export function ListingDetailPage() {
  const { listingId } = useParams<{ listingId: string }>();

  const listing = useQuery(
    api.listings.getById,
    listingId ? { listingId: listingId as Id<"listings"> } : "skip"
  );

  if (listing === undefined) {
    return <div className="text-center py-12">Loading...</div>;
  }

  if (listing === null) {
    return <div className="text-center py-12">Listing not found</div>;
  }

  return (
    <div className="bg-white rounded-lg shadow-lg overflow-hidden">
      {/* Header with Bengali name and category */}
      <div className="bg-primary text-white p-6">
        <h1 className="text-4xl font-bold">{listing.name_bn}</h1>
        <p className="text-xl mt-2">{listing.name_en}</p>
        {listing.categoryName && (
          <div className="flex items-center gap-2 mt-4 text-primary-foreground/80">
            <Tag size={18} />
            <span>{listing.categoryName}</span>
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div className="p-4 bg-gray-50 border-b flex flex-wrap gap-2 justify-center">
        <button className="flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600">
          <Navigation size={16} /> Directions
        </button>
        <button className="flex items-center gap-2 px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600">
          <Phone size={16} /> Call Now
        </button>
        <button className="flex items-center gap-2 px-4 py-2 bg-gray-700 text-white rounded-md hover:bg-gray-800">
          <MessageSquare size={16} /> Send Message
        </button>
      </div>

      {/* Body with details */}
      <div className="p-6 space-y-6">
        <div>
          <h2 className="font-semibold text-lg text-secondary mb-2">Details</h2>
          <p className="text-gray-700">{listing.description_en}</p>
          <p className="text-gray-700 mt-2">{listing.description_bn}</p>
        </div>

        <hr />

        <div>
          <h2 className="font-semibold text-lg text-secondary mb-2">Contact Information</h2>
          <div className="space-y-2">
            <div className="flex items-center gap-3 text-gray-800">
              <MapPin size={16} className="text-gray-500" />
              <span>{listing.address}</span>
            </div>
            <div className="flex items-center gap-3 text-gray-800">
              <Phone size={16} className="text-gray-500" />
              <span>{listing.phone}</span>
            </div>
            {listing.website && (
              <div className="flex items-center gap-3 text-gray-800">
                <Globe size={16} className="text-gray-500" />
                <a
                  href={listing.website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:underline"
                >
                  {listing.website}
                </a>
              </div>
            )}
          </div>
        </div>

        {listing.mediaUrls && listing.mediaUrls.length > 0 && (
          <div>
            <h2 className="font-semibold text-lg text-secondary mb-2">Gallery</h2>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              {listing.mediaUrls.map((url, index) => (
                <div key={index} className="bg-gray-200 h-40 rounded-md">
                  <img src={url} alt={`Media ${index + 1}`} className="w-full h-full object-cover rounded-md" />
                </div>
              ))}
            </div>
          </div>
        )}

        <ReviewsSection listingId={listing._id} />
      </div>
    </div>
  );
}
