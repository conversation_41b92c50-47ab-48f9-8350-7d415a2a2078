// Enhanced About Page
import { Link } from "react-router-dom";

export function About() {
  return (
    <div className="max-w-4xl mx-auto">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold text-primary mb-4">About Hyperlocal BD</h1>
        <p className="text-xl text-gray-600">
          Connecting communities with local businesses and services
        </p>
      </div>

      <div className="bg-white p-6 rounded-lg shadow-md mb-8">
        <h2 className="text-2xl font-bold mb-4">Our Mission</h2>
        <p className="mb-4">
          Hyperlocal BD is dedicated to supporting local businesses and communities in Bangladesh 
          by providing a platform that connects people with nearby services and products.
        </p>
        <p className="mb-4">
          We believe in the power of local economies and aim to make it easier for people to 
          discover, support, and engage with businesses in their neighborhoods.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-xl font-bold mb-2">Discover</h3>
          <p>
            Find local businesses, services, and products in your area with our easy-to-use search.
          </p>
        </div>
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-xl font-bold mb-2">Connect</h3>
          <p>
            Engage with local businesses through reviews, ratings, and direct communication.
          </p>
        </div>
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-xl font-bold mb-2">Support</h3>
          <p>
            Help strengthen your local economy by choosing local businesses first.
          </p>
        </div>
      </div>

      <div className="text-center">
        <Link to="/">
          <button className="px-6 py-3 bg-primary text-white rounded hover:bg-primary-hover font-semibold">
            Back to Home
          </button>
        </Link>
      </div>
    </div>
  );
}