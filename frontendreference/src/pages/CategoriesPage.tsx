// Categories Page
import { mockCategories } from "../services/mockData";

// Group categories by first letter for demo purposes
const categories = mockCategories.map((category, index) => ({
  id: category.id,
  name: category.name_en,
  icon: ["🍽️", "☕", "💻", "👕"][index % 4] || "🏢",
  count: Math.floor(Math.random() * 50) + 10 // Random count for demo
}));

export function CategoriesPage() {
  return (
    <div className="max-w-4xl mx-auto">
      <div className="text-center mb-12">
        <h1 className="text-3xl font-bold text-primary mb-2">Business Categories</h1>
        <p className="text-gray-600">Browse businesses by category</p>
      </div>
      
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
        {categories.map((category) => (
          <div 
            key={category.id} 
            className="bg-white p-6 rounded-lg shadow-md text-center hover:shadow-lg transition-shadow cursor-pointer"
          >
            <div className="text-3xl mb-2">{category.icon}</div>
            <h3 className="font-bold text-lg mb-1">{category.name}</h3>
            <p className="text-gray-600 text-sm">{category.count} businesses</p>
          </div>
        ))}
      </div>
    </div>
  );
}