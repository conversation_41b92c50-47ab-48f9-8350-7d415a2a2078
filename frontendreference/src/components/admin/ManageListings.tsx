import { useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { useTenant } from "../../hooks/useTenant";

export function ManageListings() {
  const { selectedTenant } = useTenant();
  const listings = useQuery(
    api.listings.get, 
    selectedTenant ? { tenantId: selectedTenant._id } : "skip"
  );

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h3 className="text-xl font-bold mb-4">Manage Listings</h3>
      <p className="text-gray-600 mb-6">
        Manage all listings in your tenant area. Approve, reject, or edit listings as needed.
      </p>
      
      {!selectedTenant ? (
        <div className="text-center py-4">
          <p className="text-gray-500">Please select a tenant to view listings.</p>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Listing
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Category
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {listings && listings.map((listing) => (
                <tr key={listing._id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{listing.name_en}</div>
                    <div className="text-sm text-gray-500">{listing.address}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {listing.categoryId}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                      ${listing.status === 'approved' ? 'bg-green-100 text-green-800' : 
                        listing.status === 'pending' ? 'bg-yellow-100 text-yellow-800' : 
                        'bg-red-100 text-red-800'}`}>
                      {listing.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button className="text-indigo-600 hover:text-indigo-900 mr-3">Edit</button>
                    <button className="text-red-600 hover:text-red-900">Delete</button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
      
      {listings === undefined && selectedTenant && (
        <div className="text-center py-4">
          <p className="text-gray-500">Loading listings...</p>
        </div>
      )}
      
      {listings && listings.length === 0 && selectedTenant && (
        <div className="text-center py-4">
          <p className="text-gray-500">No listings found.</p>
        </div>
      )}
    </div>
  );
}