import { LayoutDashboard, List, Star, Flag, Users, BarChart2, Setting<PERSON>, Search } from "lucide-react";
import { NavLink } from "react-router-dom";

const navItems = [
  { name: "Dashboard", icon: LayoutDashboard, href: "/admin" },
  { name: "Listings", icon: List, href: "/admin/listings" },
  { name: "Reviews", icon: Star, href: "/admin/reviews" },
  { name: "Reports", icon: Flag, href: "/admin/reports" },
  { name: "Users", icon: Users, href: "/admin/users" },
  { name: "Market Data", icon: BarChart2, href: "/admin/market-data" },
  { name: "Search", icon: Search, href: "/admin/search" },
  { name: "Settings", icon: Settings, href: "/admin/settings" },
];

export function AdminSidebar() {
  return (
    <aside className="w-64 bg-gray-800 text-white p-4 flex-shrink-0">
      <h2 className="text-2xl font-bold mb-8">Admin Panel</h2>
      <nav>
        <ul>
          {navItems.map((item) => (
            <li key={item.name}>
              <NavLink
                to={item.href}
                end
                className={({ isActive }) =>
                  `flex items-center gap-3 px-3 py-2 rounded-md my-1 transition-colors ${
                    isActive
                      ? "bg-primary text-white"
                      : "hover:bg-gray-700"
                  }`
                }
              >
                <item.icon size={20} />
                <span>{item.name}</span>
              </NavLink>
            </li>
          ))}
        </ul>
      </nav>
    </aside>
  );
}
