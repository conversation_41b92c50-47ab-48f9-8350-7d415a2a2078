import { useQuery, useMutation } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { useState } from "react";
import { toast } from "sonner";
import { useTenant } from "../../hooks/useTenant";

export function UserManagement() {
  const [email, setEmail] = useState("");
  const [selectedRole, setSelectedRole] = useState("user");
  const [status, setStatus] = useState("");
  const { selectedTenant } = useTenant();
  
  const updateUserRole = useMutation(api.users.updateUserRole);
  const tenants = useQuery(api.tenants.list);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setStatus("Updating user role...");
    
    try {
      await updateUserRole({ 
        email, 
        newRole: selectedRole as any,
        tenantId: selectedTenant ? selectedTenant._id : undefined
      });
      toast.success(`Successfully updated user role to ${selectedRole}!`);
      setStatus("");
      setEmail("");
    } catch (error: any) {
      console.error("Error updating user role:", error);
      toast.error(`Error: ${error.message}`);
      setStatus(`Error: ${error.message}`);
    }
  };

  return (
    <div className="mt-8 bg-white p-6 rounded-lg shadow-md">
      <h3 className="text-xl font-bold mb-4">User Management</h3>
      <p className="text-gray-600 mb-6">
        Update user roles and permissions.
      </p>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-700">
            User Email
          </label>
          <input
            type="email"
            id="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary"
            required
          />
        </div>
        
        <div>
          <label htmlFor="role" className="block text-sm font-medium text-gray-700">
            New Role
          </label>
          <select
            id="role"
            value={selectedRole}
            onChange={(e) => setSelectedRole(e.target.value)}
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary"
          >
            <option value="user">User</option>
            <option value="business_owner">Business Owner</option>
            <option value="area_admin">Area Admin</option>
            <option value="super_admin">Super Admin</option>
          </select>
        </div>
        
        <button
          type="submit"
          className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
        >
          Update User Role
        </button>
      </form>
      {status && (
        <div className="mt-4 p-3 bg-blue-50 text-blue-700 rounded-md">
          {status}
        </div>
      )}
    </div>
  );
}