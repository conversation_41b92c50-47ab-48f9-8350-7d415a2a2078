// SeedButton.tsx
// Component to add a seed button to the admin dashboard

import { useState } from "react";
import { toast } from "sonner";
import { useMutation } from "convex/react";
import { api } from "../../../convex/_generated/api";

export function SeedButton() {
  const [isSeeding, setIsSeeding] = useState(false);
  const seedDatabase = useMutation(api.seedMutations.seedDatabase);
  
  const handleSeed = async () => {
    if (isSeeding) return;
    
    setIsSeeding(true);
    toast.info("Seeding database with MVP data...");
    
    try {
      await seedDatabase({});
      toast.success("Database seeded successfully with MVP data!");
    } catch (error) {
      toast.error("Failed to seed database: " + (error as Error).message);
      console.error("Seed error:", error);
    } finally {
      setIsSeeding(false);
    }
  };
  
  return (
    <div className="mt-8 bg-white p-6 rounded-lg shadow-md">
      <h3 className="text-xl font-bold mb-4">Seed Database</h3>
      <p className="text-gray-600 mb-4">
        Populate the database with MVP data for Alfadanga community.
      </p>
      <button
        onClick={handleSeed}
        disabled={isSeeding}
        className={`px-4 py-2 rounded-md text-white ${
          isSeeding 
            ? "bg-gray-400 cursor-not-allowed" 
            : "bg-primary hover:bg-primary-dark"
        }`}
      >
        {isSeeding ? "Seeding..." : "Seed Database"}
      </button>
      <p className="mt-4 text-sm text-gray-500">
        This will add 12 key listings across 5 categories to get you started.
      </p>
    </div>
  );
}