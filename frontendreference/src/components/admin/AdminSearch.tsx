import { useQuery, useMutation } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { useState } from "react";
import { useTenant } from "../../hooks/useTenant";

export function AdminSearch() {
  const [searchTerm, setSearchTerm] = useState("");
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [searchType, setSearchType] = useState<"listings" | "reviews" | "users">("listings");
  
  const { selectedTenant } = useTenant();
  const search = useMutation(api.admin.searchTenantData);

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!searchTerm.trim() || !selectedTenant) return;
    
    try {
      const results = await search({ 
        term: searchTerm.trim(),
        type: searchType,
        tenantId: selectedTenant._id
      });
      setSearchResults(results);
    } catch (error) {
      console.error("Search failed:", error);
      setSearchResults([]);
    }
  };

  if (!selectedTenant) {
    return (
      <div className="mt-8 bg-white p-6 rounded-lg shadow-md">
        <h3 className="text-xl font-bold mb-4">Search Tenant Data</h3>
        <p className="text-gray-500">Please select a tenant to perform searches.</p>
      </div>
    );
  }

  return (
    <div className="mt-8 bg-white p-6 rounded-lg shadow-md">
      <h3 className="text-xl font-bold mb-4">Search Tenant Data</h3>
      
      <form onSubmit={handleSearch} className="mb-6">
        <div className="flex gap-2">
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="Enter search term..."
            className="flex-1 p-2 border rounded-md focus:ring-primary focus:border-primary"
          />
          <select
            value={searchType}
            onChange={(e) => setSearchType(e.target.value as any)}
            className="p-2 border rounded-md focus:ring-primary focus:border-primary"
          >
            <option value="listings">Listings</option>
            <option value="reviews">Reviews</option>
            <option value="users">Users</option>
          </select>
          <button
            type="submit"
            className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-hover"
          >
            Search
          </button>
        </div>
      </form>

      {searchResults.length > 0 ? (
        <div className="space-y-4">
          <p className="text-sm text-gray-500">
            Found {searchResults.length} result{searchResults.length !== 1 ? "s" : ""}
          </p>
          <ul className="border rounded-md divide-y">
            {searchResults.map((result) => (
              <li key={result._id} className="p-4">
                <div className="font-semibold">
                  {result.name_bn || result.name_en || result.name || result.email || "Unnamed"}
                </div>
                <div className="text-sm text-gray-500 mt-1">
                  ID: {result._id} | Created: {new Date(result._creationTime).toLocaleDateString()}
                </div>
                {result.address && (
                  <div className="text-sm text-gray-500">
                    Address: {result.address}
                  </div>
                )}
                {result.comment && (
                  <div className="text-sm text-gray-600 mt-1">
                    Comment: {result.comment}
                  </div>
                )}
              </li>
            ))}
          </ul>
        </div>
      ) : searchTerm ? (
        <p className="text-gray-500">No results found.</p>
      ) : null}
    </div>
  );
}