import { useQuery, useMutation } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { toast } from "sonner";
import { useTenant } from "../../hooks/useTenant";

export function PendingActions() {
  const { selectedTenant } = useTenant();
  
  const pendingListings = useQuery(
    api.admin.getPendingListings,
    selectedTenant ? { tenantId: selectedTenant._id } : "skip"
  );
  
  const approveListing = useMutation(api.admin.approveListing);
  const rejectListing = useMutation(api.admin.rejectListing);

  const handleApprove = async (listingId: string) => {
    try {
      await approveListing({ listingId });
      toast.success("Listing approved successfully!");
    } catch (error) {
      toast.error("Failed to approve listing.");
      console.error(error);
    }
  };

  const handleReject = async (listingId: string) => {
    try {
      await rejectListing({ listingId });
      toast.success("Listing rejected successfully!");
    } catch (error) {
      toast.error("Failed to reject listing.");
      console.error(error);
    }
  };

  if (!selectedTenant) {
    return (
      <div className="mt-8 bg-white p-6 rounded-lg shadow-md">
        <h3 className="text-xl font-bold mb-4">Pending Actions</h3>
        <p className="text-gray-500">Please select a tenant to view pending actions.</p>
      </div>
    );
  }

  return (
    <div className="mt-8 bg-white p-6 rounded-lg shadow-md">
      <h3 className="text-xl font-bold mb-4">Pending Actions</h3>
      {pendingListings === undefined && <p>Loading...</p>}
      {pendingListings && pendingListings.length > 0 ? (
        <ul>
          {pendingListings.map((listing) => (
            <li key={listing._id} className="border-b py-2 flex justify-between items-center">
              <div>
                <p className="font-semibold">{listing.name_en}</p>
                <p className="text-sm text-gray-500">{listing.address}</p>
              </div>
              <div>
                <button 
                  onClick={() => handleApprove(listing._id)}
                  className="px-3 py-1 bg-green-500 text-white text-sm rounded-md hover:bg-green-600 mr-2"
                >
                  Approve
                </button>
                <button 
                  onClick={() => handleReject(listing._id)}
                  className="px-3 py-1 bg-red-500 text-white text-sm rounded-md hover:bg-red-600"
                >
                  Reject
                </button>
              </div>
            </li>
          ))}
        </ul>
      ) : (
        <p className="text-gray-500">No pending actions.</p>
      )}
    </div>
  );
}
