// CsvUpload.tsx
// Component to upload CSV data to the database

import { useState, useRef, ChangeEvent } from "react";
import { toast } from "sonner";

export function CsvUpload() {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadStatus, setUploadStatus] = useState("");
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const handleFileChange = async (event: ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;
    
    if (file.type !== "text/csv" && !file.name.endsWith(".csv")) {
      toast.error("Please upload a CSV file");
      return;
    }
    
    setIsUploading(true);
    setUploadStatus("Processing CSV file...");
    
    try {
      // In a real implementation, this would parse the CSV and upload to Convex
      // For now, we'll simulate the process
      const reader = new FileReader();
      reader.onload = async (e) => {
        const content = e.target?.result as string;
        setUploadStatus("Parsing CSV data...");
        
        // Simulate processing
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Count lines (simple CSV parsing simulation)
        const lines = content.split('\n').filter(line => line.trim() !== '');
        const recordCount = lines.length - 1; // Subtract header row
        
        setUploadStatus(`Processing ${recordCount} records...`);
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        toast.success(`Successfully processed ${recordCount} records from CSV!`);
        setUploadStatus("");
      };
      
      reader.readAsText(file);
    } catch (error) {
      toast.error("Failed to process CSV: " + (error as Error).message);
      setUploadStatus("");
    } finally {
      setIsUploading(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };
  
  return (
    <div className="mt-8 bg-white p-6 rounded-lg shadow-md">
      <h3 className="text-xl font-bold mb-4">Upload CSV Data</h3>
      <p className="text-gray-600 mb-4">
        Upload a CSV file to bulk add listings to the database.
      </p>
      
      <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
        <input
          ref={fileInputRef}
          type="file"
          accept=".csv,text/csv"
          onChange={handleFileChange}
          disabled={isUploading}
          className="hidden"
          id="csv-upload"
        />
        <label
          htmlFor="csv-upload"
          className={`cursor-pointer inline-flex items-center px-4 py-2 rounded-md text-white ${
            isUploading 
              ? "bg-gray-400 cursor-not-allowed" 
              : "bg-primary hover:bg-primary-dark"
          }`}
        >
          {isUploading ? "Uploading..." : "Select CSV File"}
        </label>
        <p className="mt-2 text-sm text-gray-500">
          Upload a CSV file with listing data
        </p>
      </div>
      
      {uploadStatus && (
        <div className="mt-4 p-3 bg-blue-50 text-blue-700 rounded-md">
          {uploadStatus}
        </div>
      )}
      
      <div className="mt-4">
        <h4 className="font-medium text-gray-700 mb-2">CSV Format:</h4>
        <pre className="text-xs bg-gray-100 p-2 rounded overflow-x-auto">
{`category_name_bn,category_name_en,listing_name_bn,listing_name_en,description_bn,description_en,address,phone,website,latitude,longitude
জরুরী служба,Emergency Services,আলফাডাঙ্গা থানা,Alfadanga Police Station,আলফাডাঙ্গা উপজেলার সেবাদানকারী স্থানীয় পুলিশ স্টেশন,Local police station serving Alfadanga Upazila,Alfadanga Faridpur,01234567890,,23.75,90.4`}
        </pre>
      </div>
    </div>
  );
}