import { useQuery, useMutation } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { useState } from "react";
import { toast } from "sonner";
import { useTenant } from "../../hooks/useTenant";

export function PendingReviews() {
  const [filter, setFilter] = useState<"pending" | "approved" | "rejected">("pending");
  const { selectedTenant } = useTenant();
  
  const pendingReviews = useQuery(
    api.admin.getPendingReviews,
    selectedTenant ? { tenantId: selectedTenant._id } : "skip"
  );
  
  const approveReview = useMutation(api.admin.approveReview);
  const rejectReview = useMutation(api.admin.rejectReview);

  const handleApprove = async (reviewId: string) => {
    try {
      await approveReview({ reviewId });
      toast.success("Review approved successfully!");
    } catch (error) {
      toast.error("Failed to approve review.");
      console.error(error);
    }
  };

  const handleReject = async (reviewId: string) => {
    try {
      await rejectReview({ reviewId });
      toast.success("Review rejected successfully!");
    } catch (error) {
      toast.error("Failed to reject review.");
      console.error(error);
    }
  };

  if (!selectedTenant) {
    return (
      <div className="mt-8 bg-white p-6 rounded-lg shadow-md">
        <h3 className="text-xl font-bold mb-4">Manage Reviews</h3>
        <p className="text-gray-500">Please select a tenant to view reviews.</p>
      </div>
    );
  }

  return (
    <div className="mt-8 bg-white p-6 rounded-lg shadow-md">
      <h3 className="text-xl font-bold mb-4">Manage Reviews</h3>
      
      <div className="mb-4">
        <div className="flex space-x-2">
          <button
            className={`px-4 py-2 rounded-md ${
              filter === "pending"
                ? "bg-primary text-white"
                : "bg-gray-200 text-gray-700 hover:bg-gray-300"
            }`}
            onClick={() => setFilter("pending")}
          >
            Pending
          </button>
          <button
            className={`px-4 py-2 rounded-md ${
              filter === "approved"
                ? "bg-primary text-white"
                : "bg-gray-200 text-gray-700 hover:bg-gray-300"
            }`}
            onClick={() => setFilter("approved")}
          >
            Approved
          </button>
          <button
            className={`px-4 py-2 rounded-md ${
              filter === "rejected"
                ? "bg-primary text-white"
                : "bg-gray-200 text-gray-700 hover:bg-gray-300"
            }`}
            onClick={() => setFilter("rejected")}
          >
            Rejected
          </button>
        </div>
      </div>

      {pendingReviews === undefined && <p>Loading...</p>}
      {pendingReviews && pendingReviews.length > 0 ? (
        <ul className="space-y-4">
          {pendingReviews.map((review) => (
            <li key={review._id} className="border p-4 rounded-md">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <p className="font-semibold">{review.userName}</p>
                  <p className="text-gray-600 mt-2">{review.comment}</p>
                  <div className="flex items-center mt-2">
                    <span className="text-sm text-gray-500">
                      Rating: {review.rating}/5
                    </span>
                    <span className="ml-4 text-sm text-gray-500">
                      Status:{" "}
                      <span className="font-medium">
                        {review.isApproved ? "Approved" : "Pending"}
                      </span>
                    </span>
                  </div>
                </div>
                {filter === "pending" && (
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleApprove(review._id)}
                      className="px-3 py-1 bg-green-500 text-white text-sm rounded-md hover:bg-green-600"
                    >
                      Approve
                    </button>
                    <button
                      onClick={() => handleReject(review._id)}
                      className="px-3 py-1 bg-red-500 text-white text-sm rounded-md hover:bg-red-600"
                    >
                      Reject
                    </button>
                  </div>
                )}
              </div>
            </li>
          ))}
        </ul>
      ) : (
        <p className="text-gray-500">
          No {filter} reviews found.
        </p>
      )}
    </div>
  );
}