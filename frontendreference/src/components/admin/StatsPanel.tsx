import { useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { List, Star, Flag, Building2 } from "lucide-react";
import { useTenant } from "../../hooks/useTenant";

export function StatsPanel() {
  const { selectedTenant } = useTenant();
  
  const stats = useQuery(
    api.admin.getDashboardStats,
    selectedTenant ? { tenantId: selectedTenant._id } : "skip"
  );

  const statItems = [
    {
      name: "Pending Listings",
      value: stats?.pendingListings,
      icon: List,
      color: "bg-yellow-500",
    },
    {
      name: "Pending Reviews",
      value: stats?.pendingReviews,
      icon: Star,
      color: "bg-blue-500",
    },
    {
      name: "Open Reports",
      value: stats?.openReports,
      icon: Flag,
      color: "bg-red-500",
    },
    {
      name: "Total Listings",
      value: stats?.totalListings,
      icon: Building2,
      color: "bg-green-500",
    },
  ];

  if (!selectedTenant) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-md">
        <p className="text-gray-500">Please select a tenant to view statistics.</p>
      </div>
    );
  }

  if (stats === undefined) {
    return <div>Loading stats...</div>;
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {statItems.map((item) => (
        <div key={item.name} className="bg-white p-6 rounded-lg shadow-md flex items-center gap-4">
          <div className={`p-3 rounded-full text-white ${item.color}`}>
            <item.icon size={24} />
          </div>
          <div>
            <p className="text-gray-500">{item.name}</p>
            <p className="text-2xl font-bold">{item.value ?? "..."}</p>
          </div>
        </div>
      ))}
    </div>
  );
}
