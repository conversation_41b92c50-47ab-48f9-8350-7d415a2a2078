// src/components/home/<USER>
// Component for local news headlines

export function LocalNews() {
  const newsItems = [
    {
      id: 1,
      title: "Alfadanga UP Chairman announces new drainage project",
      excerpt: "Major infrastructure development planned for the rainy season."
    },
    {
      id: 2,
      title: "Local school wins district science fair - details inside",
      excerpt: "Students showcase innovative water purification system."
    },
    {
      id: 3,
      title: "Farmers market to open next week with special discounts",
      excerpt: "Fresh local produce available at affordable prices."
    }
  ];

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      {newsItems.map((item, _index) => (
        <div 
          key={item.id}
          className="p-4 border-b border-gray-100 last:border-b-0"
        >
          <h3 className="font-medium text-gray-900">{item.title}</h3>
          <p className="text-sm text-gray-600 mt-1">{item.excerpt}</p>
        </div>
      ))}
      <div className="p-4 bg-gray-50 text-center">
        <button className="text-primary font-medium">
          Read More News
        </button>
      </div>
    </div>
  );
}