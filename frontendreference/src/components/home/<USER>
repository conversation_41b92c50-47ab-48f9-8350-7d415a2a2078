// src/components/home/<USER>
// Component for category grids

export function CategoryGrid({ categories }: { categories: any[] }) {
  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
      {categories.map((category) => (
        <div 
          key={category.id}
          className="bg-white rounded-lg shadow-sm p-4 text-center hover:shadow-md transition-shadow cursor-pointer"
        >
          <div className="text-2xl mb-2">{category.icon}</div>
          <div className="font-medium text-gray-900 text-sm">{category.name}</div>
          <div className="text-xs text-gray-500 mt-1">{category.bnName}</div>
        </div>
      ))}
    </div>
  );
}