import { useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { useTenant } from "../../hooks/useTenant";
import { ArrowDown, ArrowUp } from "lucide-react";

export function MarketPrices() {
  const { tenantId } = useTenant();
  const prices = useQuery(
    api.marketPrices.listByTenant,
    tenantId ? { tenantId } : "skip"
  );

  return (
    <div className="my-8 p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-4 text-primary">Today's Market Prices</h2>
      {prices && prices.length > 0 ? (
        <div className="overflow-x-auto">
          <table className="w-full text-left">
            <thead>
              <tr className="border-b">
                <th className="p-2">Item (BN)</th>
                <th className="p-2">Item (EN)</th>
                <th className="p-2">Unit</th>
                <th className="p-2 text-right">Price (৳)</th>
              </tr>
            </thead>
            <tbody>
              {prices.map((item) => (
                <tr key={item._id} className="border-b hover:bg-gray-50">
                  <td className="p-2">{item.itemName_bn}</td>
                  <td className="p-2">{item.itemName_en}</td>
                  <td className="p-2">{item.unit}</td>
                  <td className="p-2 text-right font-semibold">{item.price.toFixed(2)}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <p className="text-gray-500">Market prices are not available at the moment.</p>
      )}
    </div>
  );
}
