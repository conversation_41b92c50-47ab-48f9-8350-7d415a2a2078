// src/components/home/<USER>
// Component for today's alerts and updates

export function TodayAlerts() {
  const alerts = [
    {
      id: 1,
      type: "warning",
      message: "POWER CUT: Ward 5 & 6, Today 10AM-2PM",
      icon: "⚠️"
    },
    {
      id: 2,
      type: "info",
      message: "WATER LOG: Road repair on Central Rd, avoid till 5PM",
      icon: "📢"
    },
    {
      id: 3,
      type: "celebration",
      message: "EVENT: বসন্ত মেলা at Central Field, Feb 15-17",
      icon: "🎉"
    }
  ];

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      {alerts.map((alert, index) => (
        <div 
          key={alert.id}
          className={`p-4 border-b border-gray-100 last:border-b-0 ${
            alert.type === "warning" ? "bg-yellow-50" : 
            alert.type === "celebration" ? "bg-blue-50" : "bg-white"
          }`}
        >
          <div className="flex items-start">
            <span className="text-xl mr-3">{alert.icon}</span>
            <span className="text-gray-800">{alert.message}</span>
          </div>
        </div>
      ))}
    </div>
  );
}