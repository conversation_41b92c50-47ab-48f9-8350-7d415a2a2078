import { useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { useTenant } from "../../hooks/useTenant";
import { ListingCard } from "../listings/ListingCard";
import { Doc } from "../../../convex/_generated/dataModel";

export function FeaturedListings() {
  const { tenantId } = useTenant();
  const listings = useQuery(
    api.listings.get,
    tenantId ? { tenantId } : "skip"
  );

  if (!listings || listings.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No featured listings available right now.</p>
      </div>
    );
  }

  return (
    <div className="my-8">
      <h2 className="text-2xl font-bold mb-4 text-primary">Featured Listings</h2>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {listings.slice(0, 4).map((listing: Doc<"listings">) => (
          <ListingCard key={listing._id} listing={listing} />
        ))}
      </div>
    </div>
  );
}
