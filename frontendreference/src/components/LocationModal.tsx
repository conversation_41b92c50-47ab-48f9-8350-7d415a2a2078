import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";

export function LocationModal({
  open,
  onConfirm,
}: {
  open: boolean;
  onConfirm: (tenantId: Id<"tenants">) => void;
}) {
  const tenants = useQuery(api.tenants.list);
  if (!open) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/40 p-4">
      <div className="w-full max-w-lg rounded-lg bg-white p-6 shadow-lg">
        <h2 className="mb-4 text-center text-2xl font-bold text-primary">
          Select Your Area (আপনার এলাকা নির্বাচন করুন)
        </h2>
        {tenants === undefined ? (
          <div className="p-8 text-center">Loading available areas...</div>
        ) : (
          <div className="space-y-3 max-h-[60vh] overflow-y-auto">
            {tenants.map((t) => (
              <button
                key={t._id}
                onClick={() => onConfirm(t._id)}
                className="w-full rounded-md bg-gray-50 p-4 text-left hover:bg-primary hover:text-white transition-colors"
              >
                <div className="font-semibold">{t.name}</div>
                {t.description && (
                  <div className="text-sm opacity-80">{t.description}</div>
                )}
              </button>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}