// Search Component
import { useState } from "react";

export function SearchBar() {
  const [searchTerm, setSearchTerm] = useState("");
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // In a real app, this would trigger a search
    console.log("Searching for:", searchTerm);
  };
  
  return (
    <div className="max-w-2xl mx-auto mb-8">
      <form onSubmit={handleSubmit} className="relative">
        <label htmlFor="search" className="sr-only">Search for businesses, services, or categories</label>
        <input
          type="text"
          id="search"
          name="search"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          placeholder="Search for businesses, services, or categories..."
          className="w-full px-4 py-3 pr-12 rounded-lg border border-gray-300 focus:ring-primary focus:border-primary shadow-sm"
          autoComplete="off"
        />
        <button
          type="submit"
          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-primary focus:outline-none focus:text-primary"
          aria-label="Search"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </button>
      </form>
      
      <div className="mt-4 flex flex-wrap gap-2 justify-center">
        <span className="text-sm text-gray-600">Popular:</span>
        <button 
          type="button"
          className="text-sm text-primary hover:underline focus:outline-none focus:underline"
        >
          Restaurants
        </button>
        <button 
          type="button"
          className="text-sm text-primary hover:underline focus:outline-none focus:underline"
        >
          Cafes
        </button>
        <button 
          type="button"
          className="text-sm text-primary hover:underline focus:outline-none focus:underline"
        >
          Electronics
        </button>
        <button 
          type="button"
          className="text-sm text-primary hover:underline focus:outline-none focus:underline"
        >
          Fashion
        </button>
      </div>
    </div>
  );
}