// Basic Header component with auth links
import { Link } from "react-router-dom";
import { useAuth } from "../contexts/AuthContext";

export function Header() {
  const { isAuthenticated } = useAuth();
  
  return (
    <header className="bg-white shadow-sm">
      <div className="container mx-auto px-4 py-4 flex justify-between items-center">
        <Link to="/" className="text-xl font-bold text-primary">
          Hyperlocal BD
        </Link>
        <nav>
          <ul className="flex space-x-4 items-center">
            <li>
              <Link to="/" className="text-gray-600 hover:text-primary">
                Home
              </Link>
            </li>
            <li>
              <Link to="/businesses" className="text-gray-600 hover:text-primary">
                Businesses
              </Link>
            </li>
            <li>
              <Link to="/categories" className="text-gray-600 hover:text-primary">
                Categories
              </Link>
            </li>
            <li>
              <Link to="/submit-business" className="text-gray-600 hover:text-primary">
                Submit Business
              </Link>
            </li>
            <li>
              <Link to="/about" className="text-gray-600 hover:text-primary">
                About
              </Link>
            </li>
            <li>
              <Link to="/test" className="text-gray-600 hover:text-primary">
                Test
              </Link>
            </li>
            {isAuthenticated ? (
              <li>
                <Link to="/dashboard" className="text-gray-600 hover:text-primary">
                  Dashboard
                </Link>
              </li>
            ) : (
              <li>
                <Link to="/signin" className="text-gray-600 hover:text-primary">
                  Sign In
                </Link>
              </li>
            )}
          </ul>
        </nav>
      </div>
    </header>
  );
}