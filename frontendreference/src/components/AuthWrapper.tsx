import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { useTenant } from "../hooks/useTenant";
import { TenantSelector } from "./TenantSelector";
import { LocationModal } from "./LocationModal";
import { Header } from "./Header";
import { Footer } from "./home/<USER>";
import { AdminScreen } from "../pages/AdminDashboardPage";
import { HomePage } from "../pages/HomePage";
import { ListingDetailPage } from "../pages/ListingDetailPage";
import { SearchResultsPage } from "../pages/SearchResultsPage";
import { BusinessSubmissionPage } from "../pages/BusinessSubmissionPage";
import { CategoriesPage } from "../pages/CategoriesPage";
import { UpdatesPage } from "../pages/UpdatesPage";
import { ProfilePage } from "../pages/ProfilePage";
import { Routes, Route, Navigate } from "react-router-dom";

export function AuthWrapper() {
  const { selectedTenant, setSelectedTenantId } = useTenant();
  
  const user = useQuery(api.users.getCurrentUser);

  if (user === undefined) {
    return <div>Loading user...</div>;
  }



  const isAdmin = user?.role === "area_admin" || user?.role === "super_admin";

  return (
    <div className="flex flex-col min-h-screen">
      <LocationModal
        open={!selectedTenant}
        onConfirm={(id) => setSelectedTenantId(id)}
      />
      <Header />
      <main className="flex-1 container mx-auto px-4 py-8">
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/search" element={<SearchResultsPage />} />
          <Route path="/submit-business" element={<BusinessSubmissionPage />} />
          <Route path="/listing/:listingId" element={<ListingDetailPage />} />
          <Route path="/categories" element={<CategoriesPage />} />
          <Route path="/updates" element={<UpdatesPage />} />
          <Route path="/profile" element={<ProfilePage />} />
                    {isAdmin && <Route path="/admin/*" element={<AdminScreen />} />}
          <Route path="*" element={<Navigate to="/" />} />
        </Routes>
      </main>
      <Footer />
    </div>
  );
}
