// src/components/TestScreen.tsx
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";

export default function TestScreen() {
  const message = useQuery(api.http.testQuery);
  
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col items-center justify-center">
      <h1 className="text-3xl font-bold text-primary mb-4">Convex Test</h1>
      <p className="mb-4">Message from Convex: {message || "Loading..."}</p>
    </div>
  );
}