import { Doc, Id } from "../../../convex/_generated/dataModel";

interface CategoryGridProps {
  categories: Doc<"categories">[] | undefined;
  selectedCategoryId: Id<"categories"> | undefined;
  onSelectCategory: (categoryId: Id<"categories"> | undefined) => void;
}

export function CategoryGrid({
  categories,
  selectedCategoryId,
  onSelectCategory,
}: CategoryGridProps) {
  if (!categories) {
    return <div className="text-center">Loading categories...</div>;
  }

  return (
    <div className="my-8">
      <h2 className="text-2xl font-bold mb-4 text-primary">Categories</h2>
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
        {categories.map((category) => (
          <button
            key={category._id}
            onClick={() =>
              onSelectCategory(
                selectedCategoryId === category._id ? undefined : category._id
              )
            }
            className={`p-4 rounded-lg text-center transition-colors ${
              selectedCategoryId === category._id
                ? "bg-primary text-white"
                : "bg-white shadow hover:bg-gray-100"
            }`}
          >
            {/* You might want an icon here */}
            <span className="font-semibold">{category.name_en}</span>
          </button>
        ))}
      </div>
    </div>
  );
}
