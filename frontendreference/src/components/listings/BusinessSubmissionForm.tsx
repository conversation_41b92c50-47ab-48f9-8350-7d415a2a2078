import { useState } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { useTenant } from "../../hooks/useTenant";
import { toast } from "sonner";
import { Id } from "../../../convex/_generated/dataModel";

export function BusinessSubmissionForm() {
  const { tenantId } = useTenant();
  const categories = useQuery(
    api.listings.getCategories,
    tenantId ? { tenantId } : "skip"
  );
  
  const submitBusiness = useMutation(api.listings.submitBusiness);
  
  const [formData, setFormData] = useState({
    categoryId: "",
    name_bn: "",
    name_en: "",
    description_bn: "",
    description_en: "",
    address: "",
    phone: "",
    website: "",
    lat: "",
    lng: "",
  });
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!tenantId) {
      toast.error("Please select a location first");
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      await submitBusiness({
        tenantId,
        categoryId: formData.categoryId as Id<"categories">,
        name_bn: formData.name_bn,
        name_en: formData.name_en,
        description_bn: formData.description_bn || undefined,
        description_en: formData.description_en || undefined,
        address: formData.address,
        phone: formData.phone,
        website: formData.website || undefined,
        coordinates: {
          lat: parseFloat(formData.lat),
          lng: parseFloat(formData.lng),
        },
      });
      
      toast.success("Business submitted successfully! It will be reviewed by admins.");
      
      // Reset form
      setFormData({
        categoryId: "",
        name_bn: "",
        name_en: "",
        description_bn: "",
        description_en: "",
        address: "",
        phone: "",
        website: "",
        lat: "",
        lng: "",
      });
    } catch (error) {
      toast.error("Failed to submit business: " + (error as Error).message);
      console.error(error);
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return (
    <div className="bg-white p-6 rounded-lg shadow-md max-w-2xl mx-auto">
      <h2 className="text-2xl font-bold text-primary mb-6">Submit Your Business</h2>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Category *
          </label>
          <select
            name="categoryId"
            value={formData.categoryId}
            onChange={handleChange}
            required
            className="w-full p-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
          >
            <option value="">Select a category</option>
            {categories?.map((category) => (
              <option key={category._id} value={category._id}>
                {category.name_en} / {category.name_bn}
              </option>
            ))}
          </select>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Business Name (English) *
          </label>
          <input
            type="text"
            name="name_en"
            value={formData.name_en}
            onChange={handleChange}
            required
            className="w-full p-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
            placeholder="e.g., Cafe De Dhaka"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Business Name (Bangla) *
          </label>
          <input
            type="text"
            name="name_bn"
            value={formData.name_bn}
            onChange={handleChange}
            required
            className="w-full p-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
            placeholder="e.g., ক্যাফে ডি ঢাকা"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Description (English)
          </label>
          <textarea
            name="description_en"
            value={formData.description_en}
            onChange={handleChange}
            rows={3}
            className="w-full p-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
            placeholder="Describe your business in English"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Description (Bangla)
          </label>
          <textarea
            name="description_bn"
            value={formData.description_bn}
            onChange={handleChange}
            rows={3}
            className="w-full p-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
            placeholder="বাংলায় আপনার ব্যবসার বর্ণনা দিন"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Address *
          </label>
          <input
            type="text"
            name="address"
            value={formData.address}
            onChange={handleChange}
            required
            className="w-full p-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
            placeholder="Full business address"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Phone *
          </label>
          <input
            type="tel"
            name="phone"
            value={formData.phone}
            onChange={handleChange}
            required
            className="w-full p-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
            placeholder="Business phone number"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Website
          </label>
          <input
            type="url"
            name="website"
            value={formData.website}
            onChange={handleChange}
            className="w-full p-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
            placeholder="https://yourbusiness.com"
          />
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Latitude *
            </label>
            <input
              type="number"
              name="lat"
              value={formData.lat}
              onChange={handleChange}
              required
              step="any"
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
              placeholder="23.79"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Longitude *
            </label>
            <input
              type="number"
              name="lng"
              value={formData.lng}
              onChange={handleChange}
              required
              step="any"
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
              placeholder="90.41"
            />
          </div>
        </div>
        
        <div className="flex items-center">
          <button
            type="submit"
            disabled={isSubmitting}
            className="bg-primary text-white px-6 py-2 rounded-md hover:bg-primary-hover disabled:opacity-50"
          >
            {isSubmitting ? "Submitting..." : "Submit Business"}
          </button>
        </div>
      </form>
    </div>
  );
}