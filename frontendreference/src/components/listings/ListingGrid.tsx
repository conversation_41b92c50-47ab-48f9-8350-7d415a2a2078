import { useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { Id } from "../../../convex/_generated/dataModel";
import { useTenant } from "../../hooks/useTenant";
import { ListingCard } from "./ListingCard";

interface ListingGridProps {
  categoryId: Id<"categories">;
}

export function ListingGrid({ categoryId }: ListingGridProps) {
  const { tenantId } = useTenant();
  const listings = useQuery(
    api.listings.get,
    tenantId ? { tenantId, categoryId } : "skip"
  );

  if (listings === undefined) {
    return <div className="text-center py-8">Loading listings...</div>;
  }

  if (listings.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No listings found in this category.</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
      {listings.map((listing) => (
        <ListingCard key={listing._id} listing={listing} />
      ))}
    </div>
  );
}
