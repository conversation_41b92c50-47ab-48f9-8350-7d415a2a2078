import { Doc } from "../../../convex/_generated/dataModel";
import { MapPin, Phone, Star } from "lucide-react";
import { Link } from "react-router-dom";

interface ListingCardProps {
  listing: Doc<"listings">;
}

export function ListingCard({ listing }: ListingCardProps) {
  return (
    <Link
      to={`/listing/${listing._id}`}
      className="bg-white rounded-lg shadow overflow-hidden transform hover:-translate-y-1 transition-transform duration-300 block"
    >
      <div className="p-4">
        <div className="flex justify-between items-start">
          <h3 className="font-bold text-lg text-primary">{listing.name_bn}</h3>
          {listing.isVerified && (
            <div className="flex items-center gap-1 text-sm text-yellow-500">
              <Star size={16} fill="currentColor" />
              <span>Verified</span>
            </div>
          )}
        </div>
        <p className="text-sm text-secondary">{listing.name_en}</p>
        <div className="flex items-center gap-2 mt-2 text-sm text-gray-600">
          <MapPin size={14} />
          <span>{listing.address}</span>
        </div>
        <div className="flex items-center gap-2 mt-1 text-sm text-gray-600">
          <Phone size={14} />
          <span>{listing.phone}</span>
        </div>
      </div>
    </Link>
  );
}
