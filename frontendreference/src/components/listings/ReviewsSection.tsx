import { useQuery, useMutation } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { Id } from "../../../convex/_generated/dataModel";
import { FormEvent, useState } from "react";
import { toast } from "sonner";

function StarRating({ rating, setRating, readOnly = false }: { rating: number, setRating?: (r: number) => void, readOnly?: boolean }) {
  return (
    <div className="flex">
      {[1, 2, 3, 4, 5].map((star) => (
        <svg
          key={star}
          className={`w-6 h-6 ${rating >= star ? 'text-yellow-400' : 'text-gray-300'} ${!readOnly ? 'cursor-pointer' : ''}`}
          fill="currentColor"
          viewBox="0 0 20 20"
          onClick={() => !readOnly && setRating?.(star)}
        >
          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.286 3.957a1 1 0 00.95.69h4.16c.969 0 1.371 1.24.588 1.81l-3.363 2.44a1 1 0 00-.364 1.118l1.287 3.957c.3.921-.755 1.688-1.54 1.118l-3.363-2.44a1 1 0 00-1.175 0l-3.363 2.44c-.784.57-1.838-.197-1.539-1.118l1.286-3.957a1 1 0 00-.364-1.118L2.07 9.384c-.783-.57-.38-1.81.588-1.81h4.16a1 1 0 00.95-.69L9.049 2.927z" />
        </svg>
      ))}
    </div>
  );
}

export function ReviewsSection({ listingId }: { listingId: Id<"listings"> }) {
  const reviews = useQuery(api.reviews.listByListing, { listingId });
  const addReview = useMutation(api.reviews.add);
  const [rating, setRating] = useState(0);
  const [comment, setComment] = useState("");

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    if (rating === 0) {
      toast.error("Please select a rating.");
      return;
    }
    try {
      await addReview({ listingId, rating, comment });
      toast.success("Review submitted for approval!");
      setRating(0);
      setComment("");
    } catch (error) {
      toast.error("Failed to submit review.");
      console.error(error);
    }
  };

  return (
    <div className="mt-8">
      <h2 className="font-semibold text-lg text-secondary mb-4">Reviews</h2>
      <div className="space-y-4">
        {reviews?.map((review) => (
          <div key={review._id} className="bg-gray-50 p-4 rounded-lg">
            <div className="flex justify-between items-center">
              <p className="font-semibold">{review.userName}</p>
              <StarRating rating={review.rating} readOnly />
            </div>
            <p className="text-gray-600 mt-2">{review.comment}</p>
            <p className="text-xs text-gray-400 mt-2 text-right">{new Date(review._creationTime).toLocaleDateString()}</p>
          </div>
        ))}
        {reviews?.length === 0 && <p className="text-gray-500">No reviews yet.</p>}
      </div>

      <div className="mt-8 bg-white p-6 rounded-lg shadow-md">
        <h3 className="font-semibold text-lg mb-4">Write a Review</h3>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block font-medium mb-2">Your Rating</label>
            <StarRating rating={rating} setRating={setRating} />
          </div>
          <div>
            <label htmlFor="comment" className="block font-medium mb-2">Your Comment</label>
            <textarea
              id="comment"
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              rows={4}
              className="w-full p-2 border rounded-md focus:ring-primary focus:border-primary"
              placeholder="Share your experience..."
            />
          </div>
          <button type="submit" className="bg-primary text-white px-4 py-2 rounded-md hover:bg-primary-hover">
            Submit Review
          </button>
        </form>
      </div>
    </div>
  );
}
