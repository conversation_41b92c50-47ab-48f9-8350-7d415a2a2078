import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";

interface TenantSelectorProps {
  selectTenant: (tenantId: Id<"tenants">) => void;
}

export function TenantSelector({ selectTenant }: TenantSelectorProps) {
  const tenants = useQuery(api.tenants.list);

  if (tenants === undefined) {
    return <div className="text-center p-8">Loading available areas...</div>;
  }

  return (
    <div className="flex-1 flex items-center justify-center p-4">
      <div className="max-w-lg mx-auto w-full bg-white p-8 rounded-lg shadow-md">
        <h2 className="text-2xl font-bold text-center text-primary mb-6">
          Select Your Area
        </h2>
        <div className="space-y-4">
          {tenants.map((tenant) => (
            <button
              key={tenant._id}
              onClick={() => selectTenant(tenant._id)}
              className="w-full text-left p-4 bg-gray-50 hover:bg-primary hover:text-white rounded-md transition-colors"
            >
              <h3 className="font-semibold text-lg">{tenant.name}</h3>
              <p className="text-sm text-gray-600">{tenant.description}</p>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
}
