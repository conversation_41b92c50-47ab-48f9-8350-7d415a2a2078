// Business Listing Component
import { mockListings, mockCategories } from "../services/mockData";

export interface Business {
  id: string;
  name: string;
  category: string;
  rating: number;
  reviewCount: number;
  description: string;
  location: string;
}

// Convert mock data to Business interface
const mockBusinesses: Business[] = mockListings.map(listing => {
  const category = mockCategories.find(c => c.id === listing.categoryId);
  return {
    id: listing.id,
    name: listing.name_en,
    category: category?.name_en || "Unknown",
    rating: Math.floor(Math.random() * 2) + 3, // Random rating between 3-5
    reviewCount: Math.floor(Math.random() * 100) + 10, // Random review count
    description: listing.description_en || "No description available",
    location: listing.address
  };
});

export function BusinessList() {
  return (
    <div className="max-w-4xl mx-auto">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-primary mb-2">Local Business Directory</h1>
        <p className="text-gray-600">Discover great local businesses in your area</p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {mockBusinesses.map((business) => (
          <div key={business.id} className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="p-6">
              <div className="flex justify-between items-start">
                <div>
                  <h2 className="text-xl font-bold text-gray-900">{business.name}</h2>
                  <p className="text-primary font-medium">{business.category}</p>
                </div>
                <div className="bg-primary text-white px-2 py-1 rounded text-sm font-bold">
                  {business.rating}
                </div>
              </div>
              
              <p className="mt-3 text-gray-600">{business.description}</p>
              
              <div className="mt-4 flex justify-between items-center">
                <span className="text-sm text-gray-500">{business.location}</span>
                <span className="text-sm text-gray-500">
                  {business.reviewCount} reviews
                </span>
              </div>
              
              <div className="mt-4">
                <button className="px-4 py-2 bg-primary text-white rounded hover:bg-primary-hover text-sm font-medium">
                  View Details
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
      
      <div className="mt-8 text-center">
        <button className="px-6 py-3 bg-primary text-white rounded hover:bg-primary-hover font-semibold">
          Load More Businesses
        </button>
      </div>
    </div>
  );
}