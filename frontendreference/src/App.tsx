import { Authenticated, Unauthenticated } from "convex/react";
import { SignInForm } from "./SignInForm";
import { Toaster } from "sonner";
import { TenantProvider } from "./hooks/useTenant";
import { AuthWrapper } from "./components/AuthWrapper";
import { <PERSON>rowserRouter } from "react-router-dom";

export default function App() {
  return (
    <BrowserRouter>
      <div className="min-h-screen flex flex-col bg-gray-50">
        <Toaster position="top-center" />
        <Unauthenticated>
          <div className="flex-1 flex items-center justify-center p-4">
            <div className="max-w-md mx-auto w-full bg-white p-8 rounded-lg shadow-md">
              <h1 className="text-2xl font-bold text-center text-primary mb-4">
                Hyperlocal BD
              </h1>
              <SignInForm />
            </div>
          </div>
        </Unauthenticated>
        <Authenticated>
          <TenantProvider>
            <AuthWrapper />
          </TenantProvider>
        </Authenticated>
      </div>
    </BrowserRouter>
  );
}
