// Mock data service to replace Convex during development
export interface Tenant {
  id: string;
  name: string;
  slug: string;
  description?: string;
  isActive: boolean;
  centerCoordinates: {
    lat: number;
    lng: number;
  };
}

export interface User {
  id: string;
  name: string;
  email: string;
  role: "user" | "business_owner" | "area_admin" | "super_admin" | "admin";
  tenantId?: string;
}

export interface Category {
  id: string;
  tenantId: string;
  name_bn: string;
  name_en: string;
  iconUrl?: string;
  parentId?: string;
  isActive: boolean;
}

export interface Listing {
  id: string;
  tenantId: string;
  ownerId?: string;
  categoryId: string;
  name_bn: string;
  name_en: string;
  description_bn?: string;
  description_en?: string;
  address: string;
  phone: string;
  website?: string;
  status: "pending" | "approved" | "rejected" | "archived";
  isVerified: boolean;
  priority: number;
  coordinates: {
    lat: number;
    lng: number;
  };
  mediaUrls: string[];
  submittedBy?: string;
  submittedAt?: number;
  approvedBy?: string;
  approvedAt?: number;
}

export interface Review {
  id: string;
  tenantId: string;
  listingId: string;
  userId: string;
  rating: number;
  comment?: string;
  isApproved: boolean;
}

export interface MarketPrice {
  id: string;
  tenantId: string;
  itemName_bn: string;
  itemName_en: string;
  unit: string;
  price: number;
  source?: string;
  recordedAt: number;
}

// Mock data
export const mockTenants: Tenant[] = [
  {
    id: "1",
    name: "Dhaka",
    slug: "dhaka",
    description: "The capital city of Bangladesh",
    isActive: true,
    centerCoordinates: {
      lat: 23.8103,
      lng: 90.4125
    }
  },
  {
    id: "2",
    name: "Chittagong",
    slug: "chittagong",
    description: "Major seaport city of Bangladesh",
    isActive: true,
    centerCoordinates: {
      lat: 22.3569,
      lng: 91.7832
    }
  }
];

export const mockUsers: User[] = [
  {
    id: "1",
    name: "John Doe",
    email: "<EMAIL>",
    role: "user"
  },
  {
    id: "2",
    name: "Jane Smith",
    email: "<EMAIL>",
    role: "business_owner"
  },
  {
    id: "3",
    name: "Admin User",
    email: "<EMAIL>",
    role: "admin"
  }
];

export const mockCategories: Category[] = [
  {
    id: "1",
    tenantId: "1",
    name_bn: "রেস্তোরাঁ",
    name_en: "Restaurant",
    isActive: true
  },
  {
    id: "2",
    tenantId: "1",
    name_bn: "কফি শপ",
    name_en: "Cafe",
    isActive: true
  },
  {
    id: "3",
    tenantId: "1",
    name_bn: "ইলেক্ট্রনিক্স",
    name_en: "Electronics",
    isActive: true
  },
  {
    id: "4",
    tenantId: "1",
    name_bn: "ফ্যাশন",
    name_en: "Fashion",
    isActive: true
  }
];

export const mockListings: Listing[] = [
  {
    id: "1",
    tenantId: "1",
    ownerId: "2",
    categoryId: "1",
    name_bn: "ঢাকা কফি হাউস",
    name_en: "Dhaka Coffee House",
    description_en: "Specialty coffee shop with locally sourced beans and cozy atmosphere.",
    address: "House 12, Road 12, Gulshan 1, Dhaka",
    phone: "+8801712345678",
    website: "https://dhakacoffee.com",
    status: "approved",
    isVerified: true,
    priority: 5,
    coordinates: {
      lat: 23.7808875,
      lng: 90.4125005
    },
    mediaUrls: []
  },
  {
    id: "2",
    tenantId: "1",
    categoryId: "3",
    name_bn: "টেক গ্যাজেটস বিডি",
    name_en: "Tech Gadgets BD",
    description_en: "Latest smartphones, laptops, and tech accessories at competitive prices.",
    address: "Shop 5, Dhanmondi Road 10, Dhaka",
    phone: "+8801812345678",
    status: "approved",
    isVerified: true,
    priority: 3,
    coordinates: {
      lat: 23.7450005,
      lng: 90.3825005
    },
    mediaUrls: []
  }
];

export const mockReviews: Review[] = [
  {
    id: "1",
    tenantId: "1",
    listingId: "1",
    userId: "1",
    rating: 5,
    comment: "Great coffee and cozy atmosphere!",
    isApproved: true
  },
  {
    id: "2",
    tenantId: "1",
    listingId: "2",
    userId: "1",
    rating: 4,
    comment: "Good selection of products, competitive prices.",
    isApproved: true
  }
];

export const mockMarketPrices: MarketPrice[] = [
  {
    id: "1",
    tenantId: "1",
    itemName_bn: "আলু",
    itemName_en: "Potato",
    unit: "kg",
    price: 30,
    recordedAt: Date.now()
  },
  {
    id: "2",
    tenantId: "1",
    itemName_bn: "ডিম",
    itemName_en: "Egg",
    unit: "piece",
    price: 12,
    recordedAt: Date.now()
  }
];

// Mock query hooks
export const useQuery = () => {
  return undefined;
};

export const useMutation = () => {
  return () => Promise.resolve();
};