// populate-home-screen-data.ts
// <PERSON><PERSON>t to populate the database with sample data for the new home screen

console.log("=== Populating Home Screen Sample Data ===\n");

const sampleData = {
  alerts: [
    {
      type: "warning",
      message: "POWER CUT: Ward 5 & 6, Today 10AM-2PM",
      icon: "⚠️"
    },
    {
      type: "info",
      message: "WATER LOG: Road repair on Central Rd, avoid till 5PM",
      icon: "📢"
    },
    {
      type: "celebration",
      message: "EVENT: বসন্ত মেলা at Central Field, Feb 15-17",
      icon: "🎉"
    }
  ],
  
  essentialServices: [
    { name: "Police Station", bnName: "থানা", icon: "👮‍♂️" },
    { name: "Fire Service", bnName: "ফায়ার সার্ভিস", icon: "🚒" },
    { name: "Hospital", bnName: "হাসপাতাল", icon: "🏥" },
    { name: "PBD Office", bnName: "পল্লীবিদ্যুৎ", icon: "⚡" }
  ],
  
  governmentOffices: [
    { name: "Upazila Parishad", bnName: "উপজেলা পরিষদ", icon: "🏢" },
    { name: "Post Office", bnName: "ডাকঘর", icon: "📮" },
    { name: "Land Office", bnName: "জমি অফিস", icon: "📄" },
    { name: "Water Authority", bnName: "পানি অধিদপ্তর", icon: "💧" }
  ],
  
  localLife: [
    { name: "Central Mosque", bnName: "কেন্দ্রীয় মসজিদ", icon: "" },
    { name: "Alfadanga Park", bnName: "আলফাডাঙ্গা পার্ক", icon: "🏞️" },
    { name: "Public Library", bnName: "পাবলিক লাইব্রেরি", icon: "📚" },
    { name: "Bazaar Market", bnName: "বাজার", icon: "🛒" }
  ],
  
  newsItems: [
    {
      title: "Alfadanga UP Chairman announces new drainage project",
      excerpt: "Major infrastructure development planned for the rainy season."
    },
    {
      title: "Local school wins district science fair - details inside",
      excerpt: "Students showcase innovative water purification system."
    },
    {
      title: "Farmers market to open next week with special discounts",
      excerpt: "Fresh local produce available at affordable prices."
    }
  ],
  
  communityTestimonial: {
    quote: "I found a reliable plumber in 10 mins using this app! 😊",
    author: "রহিমা, ওয়ার্ড ৩"
  }
};

console.log("Sample data structure for the home screen:");
console.log(JSON.stringify(sampleData, null, 2));

console.log("\n=== How to Use This Data ===");
console.log("1. This data structure can be used to populate the database");
console.log("2. In a real implementation, you would:");
console.log("   - Create listings for each service/office/location");
console.log("   - Add metadata for alerts and community updates");
console.log("   - Create news articles as listings with a 'news' category");
console.log("   - Store testimonials in a dedicated collection");
console.log("3. The frontend components will query this data to populate the home screen");

console.log("\n=== Next Steps ===");
console.log("1. Use the admin dashboard to create these listings");
console.log("2. Add metadata for alerts and updates");
console.log("3. Create a 'news' category for news articles");
console.log("4. Test the home screen with real data");