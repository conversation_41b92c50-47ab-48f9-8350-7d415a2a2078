// test-admin-access.js
console.log("To test admin access:");
console.log("1. Make sure you're logged in with <NAME_EMAIL>");
console.log("2. Navigate to http://localhost:5173/admin in your browser");
console.log("3. If you see the admin panel, you have access");
console.log("4. If you get an error about tenant association, the user needs to be assigned to a tenant");

console.log("\nTo check if a user is properly set up as admin:");
console.log("1. In the Convex dashboard, run this query:");
console.log(`
query {
  users:getCurrentUser {
    role
    tenantId
  }
}
`);