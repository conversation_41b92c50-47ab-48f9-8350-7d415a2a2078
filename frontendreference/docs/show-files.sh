#!/bin/bash
# show-files.sh
# Script to show all the files created for the MVP launch

echo "=== Hyperlocal Bangladesh App - MVP Launch Files ==="
echo ""

echo "Documentation:"
echo "  - MVP_LAUNCH_GUIDE.md: Complete step-by-step launch guide"
echo "  - MVP_<PERSON><PERSON>CH_CHECKLIST.md: Checklist to track your progress"
echo "  - SEEDING_INSTRUCTIONS.md: Detailed instructions for seeding the database"
echo "  - MVP_LAUNCH_COMPLETE_GUIDE.md: Complete overview of all files and next steps"
echo ""

echo "Data Files:"
echo "  - populate-mvp-data.js: Sample data for all MVP listings"
echo "  - convex/seedData.ts: Updated seed data with MVP content"
echo "  - convex/seedMutations.ts: Updated seed mutations with proper category associations"
echo ""

echo "Scripts:"
echo "  - seed-mvp.sh: Shell script to run the seed mutation"
echo "  - run-seed.ts: TypeScript script with instructions for running seed"
echo "  - verify-seed.ts: Script to verify seeding worked"
echo "  - package.json: Updated with seed script (npm run seed)"
echo ""

echo "=== Quick Start Commands ==="
echo ""
echo "1. Make sure your development servers are running:"
echo "   npx convex dev  # In one terminal"
echo "   npm run dev     # In another terminal"
echo ""
echo "2. Seed the database with MVP data:"
echo "   npm run seed"
echo "   OR"
echo "   ./seed-mvp.sh"
echo "   OR"
echo "   npx convex run seedMutations:seedDatabase '{}'"
echo ""
echo "3. Verify the seeding worked:"
echo "   npx tsx verify-seed.ts"
echo "   OR check the admin dashboard at http://localhost:5173/admin"
echo ""
echo "4. If automated seeding fails, manually create data through the admin dashboard:"
echo "   - Navigate to http://localhost:5173/admin"
echo "   - Sign in with your admin account"
echo "   - Use sample data from populate-mvp-data.js"