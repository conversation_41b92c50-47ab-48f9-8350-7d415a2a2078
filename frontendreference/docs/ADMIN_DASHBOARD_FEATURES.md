# Admin Dashboard Features

## Overview
This document explains the easy-to-use features added to the admin dashboard for seeding and managing data.

## Features

### 1. Seed Database Button
- **Location**: Admin Dashboard homepage
- **Purpose**: One-click seeding of the database with MVP data
- **What it does**: 
  - Creates 1 tenant (Alfadanga)
  - Creates 5 categories (Emergency Services, Government Offices, Utilities, Historical & Religious Sites, Public Places)
  - Creates 12 key listings including police station, fire service, health complex, etc.
  - Creates sample reviews and market prices
- **How to use**: Click the "Seed Database" button on the admin dashboard homepage

### 2. CSV Upload
- **Location**: Admin Dashboard homepage
- **Purpose**: Bulk upload listings from a CSV file
- **Supported format**: CSV with columns for category, listing name (Bangla/English), description, address, contact info, coordinates
- **How to use**: 
  1. Prepare a CSV file with your data (see format below)
  2. Click "Select CSV File" on the admin dashboard
  3. Choose your CSV file
  4. Wait for processing to complete

### 3. CSV Format
The CSV file should have the following columns:
```
category_name_bn,category_name_en,listing_name_bn,listing_name_en,description_bn,description_en,address,phone,website,latitude,longitude
```

Example row:
```
জরুরী служба,Emergency Services,আলফাডাঙ্গা থানা,Alfadanga Police Station,আলফাডাঙ্গা উপজেলার সেবাদানকারী স্থানীয় পুলিশ স্টেশন,Local police station serving Alfadanga Upazila,Alfadanga Faridpur,***********,,23.75,90.4
```

## Benefits
1. **No coding required**: Everything can be done through the admin interface
2. **One-click seeding**: Get started with MVP data instantly
3. **Bulk upload**: Add many listings at once with CSV
4. **Easy to use**: Simple interface with clear instructions
5. **Flexible**: Add data manually, via seed button, or via CSV upload

## Next Steps
1. Navigate to http://localhost:5173/admin
2. Sign in with your admin account
3. Try the "Seed Database" button to populate with sample data
4. Use the CSV upload to add your own data
5. Manually add/edit listings through the "Listings" section