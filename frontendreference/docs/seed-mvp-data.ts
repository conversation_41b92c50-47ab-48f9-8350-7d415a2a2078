// seed-mvp-data.ts
// <PERSON>ript to seed the Hyperlocal Bangladesh app with MVP data
// Run this script using: npx tsx seed-mvp-data.ts

import { convex } from 'convex';

async function seedMvpData() {
  console.log('=== Seeding Hyperlocal Bangladesh App with MVP Data ===\n');
  
  try {
    console.log('Running seedDatabase mutation...');
    
    // Run the existing seedDatabase mutation which will populate the database
    // with the data from seedData.ts
    await convex.mutation({
      handler: async (ctx) => {
        // First, let's clear existing data (optional, depending on your needs)
        const tables: string[] = [
          "tenants",
          "user_metadata",
          "categories",
          "listings",
          "reviews",
          "marketPrices",
        ];
        
        for (const table of tables) {
          const docs = await ctx.db.query(table as any).collect();
          await Promise.all(docs.map((doc) => ctx.db.delete(doc._id)));
        }
        
        // Import the seed data
        const { tenants, categories, listings, reviews, marketPrices } = await import('./seedData');
        
        // Seed tenants
        const tenantIds = await Promise.all(
          tenants.map((tenant) => 
            ctx.db.insert("tenants", {
              ...tenant,
              // Update tenant to be Alfadanga focused
              name: "Alfadanga",
              slug: "alfadanga",
              description: "Alfadanga Upazila, Faridpur",
            })
          )
        );
        
        console.log(`✓ Created tenant: Alfadanga`);
        
        // Seed categories with our MVP categories
        const mvpCategories = [
          {
            name_bn: "জরুরী служба",
            name_en: "Emergency Services",
            isActive: true,
          },
          {
            name_bn: "সরকারী অফিস",
            name_en: "Government Offices",
            isActive: true,
          },
          {
            name_bn: "উপযোগিতা",
            name_en: "Utilities",
            isActive: true,
          },
          {
            name_bn: "ঐতিহাসিক ও ধর্মীয় স্থান",
            name_en: "Historical & Religious Sites",
            isActive: true,
          },
          {
            name_bn: "পাবলিক প্লেস",
            name_en: "Public Places",
            isActive: true,
          }
        ];
        
        const categoryIds = await Promise.all(
          mvpCategories.map((category) =>
            ctx.db.insert("categories", { ...category, tenantId: tenantIds[0] })
          )
        );
        
        console.log(`✓ Created ${mvpCategories.length} categories`);
        
        // Seed listings with our MVP listings
        const now = Date.now();
        
        // Emergency Services listings
        const emergencyServices = [
          {
            name_bn: "আলফাডাঙ্গা থানা",
            name_en: "Alfadanga Police Station",
            description_bn: "আলফাডাঙ্গা উপজেলার সেবাদানকারী স্থানীয় পুলিশ স্টেশন",
            description_en: "Local police station serving Alfadanga Upazila",
            address: "Alfadanga, Faridpur",
            phone: "01234567890",
            status: "approved",
            isVerified: true,
            priority: 10,
            coordinates: { lat: 23.75, lng: 90.4 },
            mediaUrls: [],
          },
          {
            name_bn: "আলফাডাঙ্গা ফায়ার সার্ভিস",
            name_en: "Alfadanga Fire Service",
            description_bn: "আলফাডাঙ্গার জন্য অগ্নিনির্বাপণ জরুরি সেবা",
            description_en: "Fire emergency services for Alfadanga",
            address: "Alfadanga, Faridpur",
            phone: "01234567891",
            status: "approved",
            isVerified: true,
            priority: 10,
            coordinates: { lat: 23.75, lng: 90.4 },
            mediaUrls: [],
          },
          {
            name_bn: "উপজেলা স্বাস্থ্য কমপ্লেক্স",
            name_en: "Upazila Health Complex",
            description_bn: "উপজেলার জন্য প্রাথমিক স্বাস্থ্যসেবা সুবিধা",
            description_en: "Primary healthcare facility for the upazila",
            address: "Alfadanga, Faridpur",
            phone: "01234567892",
            status: "approved",
            isVerified: true,
            priority: 10,
            coordinates: { lat: 23.75, lng: 90.4 },
            mediaUrls: [],
          }
        ];
        
        // Government Offices listings
        const governmentOffices = [
          {
            name_bn: "আলফাডাঙ্গা উপজেলা পরিষদ",
            name_en: "Alfadanga Upazila Parishad",
            description_bn: "আলফাডাঙ্গা উপজেলার জন্য স্থানীয় সরকারি অফিস",
            description_en: "Local government office for Alfadanga Upazila",
            address: "Alfadanga, Faridpur",
            phone: "01234567893",
            status: "approved",
            isVerified: true,
            priority: 10,
            coordinates: { lat: 23.75, lng: 90.4 },
            mediaUrls: [],
          },
          {
            name_bn: "আলফাডাঙ্গা জমি অফিস",
            name_en: "Alfadanga Land Office",
            description_bn: "জমির রেকর্ড এবং সেবার জন্য অফিস",
            description_en: "Office for land records and services",
            address: "Alfadanga, Faridpur",
            phone: "01234567894",
            status: "approved",
            isVerified: true,
            priority: 10,
            coordinates: { lat: 23.75, lng: 90.4 },
            mediaUrls: [],
          }
        ];
        
        // Utilities listings
        const utilities = [
          {
            name_bn: "আলফাডাঙ্গা পল্লীবিদ্যুত অফিস",
            name_en: "Alfadanga Palli Bidyut Office",
            description_bn: "গ্রামীণ এলাকার জন্য বিদ্যুৎ সেবা অফিস",
            description_en: "Electricity service office for rural areas",
            address: "Alfadanga, Faridpur",
            phone: "01234567895",
            status: "approved",
            isVerified: true,
            priority: 10,
            coordinates: { lat: 23.75, lng: 90.4 },
            mediaUrls: [],
          },
          {
            name_bn: "আলফাডাঙ্গা পানি অধিদপ্তর",
            name_en: "Alfadanga Water Supply Authority",
            description_bn: "পানি সরবরাহ এবং পরিচালনা কর্তৃপক্ষ",
            description_en: "Water supply and management authority",
            address: "Alfadanga, Faridpur",
            phone: "01234567896",
            status: "approved",
            isVerified: true,
            priority: 10,
            coordinates: { lat: 23.75, lng: 90.4 },
            mediaUrls: [],
          }
        ];
        
        // Historical & Religious Sites listings
        const historicalSites = [
          {
            name_bn: "আলফাডাঙ্গার প্রাচীন মসজিদ",
            name_en: "Ancient Mosque of Alfadanga",
            description_bn: "স্থাপত্যগত গুরুত্বপূর্ণ ঐতিহাসিক মসজিদ",
            description_en: "Historic mosque with architectural significance",
            address: "Alfadanga, Faridpur",
            phone: "",
            status: "approved",
            isVerified: true,
            priority: 10,
            coordinates: { lat: 23.75, lng: 90.4 },
            mediaUrls: [],
          },
          {
            name_bn: "স্থানীয় শহীদ মিনার",
            name_en: "Local Martyrs' Memorial",
            description_bn: "স্থানীয় মুক্তিযোদ্ধাদের উৎসর্গীকৃত স্মৃতিস্তম্ভ",
            description_en: "Memorial dedicated to local freedom fighters",
            address: "Alfadanga, Faridpur",
            phone: "",
            status: "approved",
            isVerified: true,
            priority: 10,
            coordinates: { lat: 23.75, lng: 90.4 },
            mediaUrls: [],
          }
        ];
        
        // Public Places listings
        const publicPlaces = [
          {
            name_bn: "আলফাডাঙ্গা কেন্দ্রীয় উদ্যান",
            name_en: "Alfadanga Central Park",
            description_bn: "আলফাডাঙ্গার প্রধান পাবলিক পার্ক",
            description_en: "Main public park in Alfadanga",
            address: "Alfadanga, Faridpur",
            phone: "",
            status: "approved",
            isVerified: true,
            priority: 10,
            coordinates: { lat: 23.75, lng: 90.4 },
            mediaUrls: [],
          },
          {
            name_bn: "আলফাডাঙ্গা পাবলিক লাইব্রেরি",
            name_en: "Alfadanga Public Library",
            description_bn: "বই এবং পড়ার জায়গা সহ পাবলিক লাইব্রেরি",
            description_en: "Public library with books and reading space",
            address: "Alfadanga, Faridpur",
            phone: "01234567897",
            status: "approved",
            isVerified: true,
            priority: 10,
            coordinates: { lat: 23.75, lng: 90.4 },
            mediaUrls: [],
          }
        ];
        
        // Combine all listings
        const allListings = [
          ...emergencyServices,
          ...governmentOffices,
          ...utilities,
          ...historicalSites,
          ...publicPlaces
        ];
        
        // Create all listings with the first category (we'll update them later)
        const listingIds = await Promise.all(
          allListings.map((listing) =>
            ctx.db.insert("listings", {
              ...listing,
              tenantId: tenantIds[0],
              categoryId: categoryIds[0], // Will be updated later
              ownerId: "user_placeholder_id" as any,
              submittedBy: "user_placeholder_id" as any,
              submittedAt: now,
            })
          )
        );
        
        console.log(`✓ Created ${allListings.length} listings`);
        
        // Update listings with correct categories
        // This is a simplified approach - in a real implementation, you'd map each listing to its correct category
        for (let i = 0; i < allListings.length; i++) {
          let categoryId = categoryIds[0]; // Default to first category
          
          // Assign categories based on listing type
          if (i < emergencyServices.length) {
            categoryId = categoryIds[0]; // Emergency Services
          } else if (i < emergencyServices.length + governmentOffices.length) {
            categoryId = categoryIds[1]; // Government Offices
          } else if (i < emergencyServices.length + governmentOffices.length + utilities.length) {
            categoryId = categoryIds[2]; // Utilities
          } else if (i < emergencyServices.length + governmentOffices.length + utilities.length + historicalSites.length) {
            categoryId = categoryIds[3]; // Historical & Religious Sites
          } else {
            categoryId = categoryIds[4]; // Public Places
          }
          
          await ctx.db.patch(listingIds[i], {
            categoryId: categoryId
          });
        }
        
        // Create Community Updates listing
        const communityUpdatesListingId = await ctx.db.insert("listings", {
          tenantId: tenantIds[0],
          categoryId: categoryIds[4], // Public Places
          name_bn: "কমিউনিটি আপডেটস",
          name_en: "Community Updates",
          description_bn: "আলফাডাঙ্গা সম্প্রদায়ের জন্য সময়মতো আপডেট",
          description_en: "Timely updates for the Alfadanga community",
          address: "Alfadanga, Faridpur",
          phone: "",
          status: "approved",
          isVerified: true,
          priority: 10,
          coordinates: { lat: 23.75, lng: 90.4 },
          mediaUrls: [],
          ownerId: "user_placeholder_id" as any,
          submittedBy: "user_placeholder_id" as any,
          submittedAt: now,
        });
        
        console.log('✓ Created Community Updates listing');
        
        // Add metadata to Community Updates listing
        const metadataEntries = [
          {
            listingId: communityUpdatesListingId,
            key: "power_outage_jan_25",
            value: JSON.stringify({
              "date": "2024-01-25",
              "area": "Ward 5, 6",
              "time": "10:00 AM - 2:00 PM"
            })
          },
          {
            listingId: communityUpdatesListingId,
            key: "local_fair",
            value: JSON.stringify({
              "title": "বসন্ত মেলা",
              "date": "2024-02-15",
              "location": "Central Field"
            })
          }
        ];
        
        for (const metadata of metadataEntries) {
          await ctx.db.insert("listingMetadata", metadata);
        }
        
        console.log('✓ Added metadata to Community Updates listing');
        
        // Seed some sample market prices for Alfadanga
        const alfadangaMarketPrices = [
          {
            itemName_bn: "আলু",
            itemName_en: "Potato",
            unit: "kg",
            price: 30,
            recordedAt: now,
          },
          {
            itemName_bn: "পেঁয়াজ",
            itemName_en: "Onion",
            unit: "kg",
            price: 50,
            recordedAt: now,
          },
          {
            itemName_bn: "রুই মাছ",
            itemName_en: "Rui Fish",
            unit: "kg",
            price: 200,
            recordedAt: now,
          }
        ];
        
        await Promise.all(
          alfadangaMarketPrices.map((price) =>
            ctx.db.insert("marketPrices", { ...price, tenantId: tenantIds[0] })
          )
        );
        
        console.log(`✓ Created ${alfadangaMarketPrices.length} market price entries`);
        
        return {
          success: true,
          message: `Seeded database with ${mvpCategories.length} categories, ${allListings.length + 1} listings, and ${alfadangaMarketPrices.length} market prices`
        };
      }
    });
    
    console.log('\n=== Seeding Complete ===');
    console.log('MVP data seeding completed successfully!');
    console.log('\nNext steps:');
    console.log('1. Navigate to http://localhost:5173/admin');
    console.log('2. Sign in with your admin account');
    console.log('3. Browse the categories and listings');
    console.log('4. Test the Community Updates feature');
    console.log('5. Add more listings to reach 100-150 total');
    
  } catch (error) {
    console.error('Error during seeding:', error);
  }
}

// Run the seeding function
seedMvpData();