// populate-mvp-data.js
// <PERSON>ript to help populate the Hyperlocal Bangladesh app with curated MVP data

console.log("=== Hyperlocal Bangladesh App - MVP Data Population Script ===\n");

console.log("This script will help you populate your app with curated data for the MVP.");
console.log("Follow these steps:\n");

console.log("1. Make sure your development servers are running:");
console.log("   - Convex: http://127.0.0.1:3210");
console.log("   - Frontend: http://localhost:5173\n");

console.log("2. Sign in to the app as an admin user (<EMAIL>)\n");

console.log("3. Use the admin dashboard to create the following categories:");
console.log("   - Emergency Services (জরুরী служба)");
console.log("   - Government Offices (সরকারী অফিস)");
console.log("   - Utilities (উপযোগিতা)");
console.log("   - Historical & Religious Sites (ঐতিহাসিক ও ধর্মীয় স্থান)");
console.log("   - Public Places (পাবলিক প্লেস)\n");

console.log("4. Sample data to add for each category:\n");

// Emergency Services
console.log("=== Emergency Services ===");
const emergencyServices = [
  {
    name_en: "Alfadanga Police Station",
    name_bn: "আলফাডাঙ্গা থানা",
    address: "Alfadanga, Faridpur",
    phone: "01234567890",
    description_en: "Local police station serving Alfadanga Upazila",
    description_bn: "আলফাডাঙ্গা উপজেলার সেবাদানকারী স্থানীয় পুলিশ স্টেশন"
  },
  {
    name_en: "Alfadanga Fire Service",
    name_bn: "আলফাডাঙ্গা ফায়ার সার্ভিস",
    address: "Alfadanga, Faridpur",
    phone: "01234567891",
    description_en: "Fire emergency services for Alfadanga",
    description_bn: "আলফাডাঙ্গার জন্য অগ্নিনির্বাপণ জরুরি সেবা"
  },
  {
    name_en: "Upazila Health Complex",
    name_bn: "উপজেলা স্বাস্থ্য কমপ্লেক্স",
    address: "Alfadanga, Faridpur",
    phone: "01234567892",
    description_en: "Primary healthcare facility for the upazila",
    description_bn: "উপজেলার জন্য প্রাথমিক স্বাস্থ্যসেবা সুবিধা"
  }
];

emergencyServices.forEach((service, index) => {
  console.log(`${index + 1}. ${service.name_en} (${service.name_bn})`);
  console.log(`   Address: ${service.address}`);
  console.log(`   Phone: ${service.phone}`);
  console.log(`   Description: ${service.description_en}`);
  console.log(`   বর্ণনা: ${service.description_bn}\n`);
});

// Government Offices
console.log("=== Government Offices ===");
const governmentOffices = [
  {
    name_en: "Alfadanga Upazila Parishad",
    name_bn: "আলফাডাঙ্গা উপজেলা পরিষদ",
    address: "Alfadanga, Faridpur",
    phone: "01234567893",
    description_en: "Local government office for Alfadanga Upazila",
    description_bn: "আলফাডাঙ্গা উপজেলার জন্য স্থানীয় সরকারি অফিস"
  },
  {
    name_en: "Alfadanga Land Office",
    name_bn: "আলফাডাঙ্গা জমি অফিস",
    address: "Alfadanga, Faridpur",
    phone: "01234567894",
    description_en: "Office for land records and services",
    description_bn: "জমির রেকর্ড এবং সেবার জন্য অফিস"
  }
];

governmentOffices.forEach((office, index) => {
  console.log(`${index + 1}. ${office.name_en} (${office.name_bn})`);
  console.log(`   Address: ${office.address}`);
  console.log(`   Phone: ${office.phone}`);
  console.log(`   Description: ${office.description_en}`);
  console.log(`   বর্ণনা: ${office.description_bn}\n`);
});

// Utilities
console.log("=== Utilities ===");
const utilities = [
  {
    name_en: "Alfadanga Palli Bidyut Office",
    name_bn: "আলফাডাঙ্গা পল্লীবিদ্যুত অফিস",
    address: "Alfadanga, Faridpur",
    phone: "01234567895",
    description_en: "Electricity service office for rural areas",
    description_bn: "গ্রামীণ এলাকার জন্য বিদ্যুৎ সেবা অফিস"
  },
  {
    name_en: "Alfadanga Water Supply Authority",
    name_bn: "আলফাডাঙ্গা পানি অধিদপ্তর",
    address: "Alfadanga, Faridpur",
    phone: "01234567896",
    description_en: "Water supply and management authority",
    description_bn: "পানি সরবরাহ এবং পরিচালনা কর্তৃপক্ষ"
  }
];

utilities.forEach((utility, index) => {
  console.log(`${index + 1}. ${utility.name_en} (${utility.name_bn})`);
  console.log(`   Address: ${utility.address}`);
  console.log(`   Phone: ${utility.phone}`);
  console.log(`   Description: ${utility.description_en}`);
  console.log(`   বর্ণনা: ${utility.description_bn}\n`);
});

// Historical & Religious Sites
console.log("=== Historical & Religious Sites ===");
const historicalSites = [
  {
    name_en: "Ancient Mosque of Alfadanga",
    name_bn: "আলফাডাঙ্গার প্রাচীন মসজিদ",
    address: "Alfadanga, Faridpur",
    phone: "N/A",
    description_en: "Historic mosque with architectural significance",
    description_bn: "স্থাপত্যগত গুরুত্বপূর্ণ ঐতিহাসিক মসজিদ"
  },
  {
    name_en: "Local Martyrs' Memorial",
    name_bn: "স্থানীয় শহীদ মিনার",
    address: "Alfadanga, Faridpur",
    phone: "N/A",
    description_en: "Memorial dedicated to local freedom fighters",
    description_bn: "স্থানীয় মুক্তিযোদ্ধাদের উৎসর্গীকৃত স্মৃতিস্তম্ভ"
  }
];

historicalSites.forEach((site, index) => {
  console.log(`${index + 1}. ${site.name_en} (${site.name_bn})`);
  console.log(`   Address: ${site.address}`);
  console.log(`   Phone: ${site.phone}`);
  console.log(`   Description: ${site.description_en}`);
  console.log(`   বর্ণনা: ${site.description_bn}\n`);
});

// Public Places
console.log("=== Public Places ===");
const publicPlaces = [
  {
    name_en: "Alfadanga Central Park",
    name_bn: "আলফাডাঙ্গা কেন্দ্রীয় উদ্যান",
    address: "Alfadanga, Faridpur",
    phone: "N/A",
    description_en: "Main public park in Alfadanga",
    description_bn: "আলফাডাঙ্গার প্রধান পাবলিক পার্ক"
  },
  {
    name_en: "Alfadanga Public Library",
    name_bn: "আলফাডাঙ্গা পাবলিক লাইব্রেরি",
    address: "Alfadanga, Faridpur",
    phone: "01234567897",
    description_en: "Public library with books and reading space",
    description_bn: "বই এবং পড়ার জায়গা সহ পাবলিক লাইব্রেরি"
  }
];

publicPlaces.forEach((place, index) => {
  console.log(`${index + 1}. ${place.name_en} (${place.name_bn})`);
  console.log(`   Address: ${place.address}`);
  console.log(`   Phone: ${place.phone}`);
  console.log(`   Description: ${place.description_en}`);
  console.log(`   বর্ণনা: ${place.description_bn}\n`);
});

console.log("=== Community Updates Example ===");
console.log("Create a listing called 'Community Updates' and add metadata like:");
console.log("Key: power_outage_jan_25");
console.log('Value: {"date": "2024-01-25", "area": "Ward 5, 6", "time": "10:00 AM - 2:00 PM"}');
console.log("");
console.log("Key: local_fair");
console.log('Value: {"title": "বসন্ত মেলা", "date": "2024-02-15", "location": "Central Field"}');

console.log("\n=== Next Steps ===");
console.log("1. Navigate to http://localhost:5173/admin in your browser");
console.log("2. Create the categories listed above");
console.log("3. Add the sample listings for each category");
console.log("4. Add the Community Updates listing with metadata");
console.log("5. Continue adding more listings to reach 100-150 total");
console.log("6. Test the app and prepare for launch in the Alfadanga community");