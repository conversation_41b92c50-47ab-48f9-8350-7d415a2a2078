// test-user-role.js
const { convex } = require('convex');

// This would normally be run in the Convex dashboard or via a script
console.log("To test user role updates, you can run this mutation in the Convex dashboard:");

console.log(`
mutation {
  users:updateUserRole(email: "<EMAIL>", newRole: "super_admin")
}
`);

console.log("\nOr if you want to assign a tenant as well:");
console.log(`
mutation {
  users:updateUserRole(
    email: "<EMAIL>", 
    newRole: "area_admin",
    tenantId: "tenant_id_here"
  )
}
`);