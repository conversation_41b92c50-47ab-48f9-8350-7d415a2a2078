# Hyperlocal Bangladesh App - Admin Setup & Troubleshooting

## Current Status

The application has been updated with improved admin functionality:
- User management is now integrated into the admin panel
- Tracking for listing submissions and approvals
- Super admin access to all tenants
- Area admin access to specific tenants

## Network Issues with Convex

You're experiencing network errors during Convex deployment. This is a common issue that can be caused by:

1. **Temporary connectivity issues**
2. **Firewall or proxy restrictions**
3. **Convex service issues**
4. **Large deployment payloads**

## Troubleshooting Steps

### 1. Check Convex Status
Visit https://status.convex.dev to see if there are any ongoing service issues.

### 2. Verify Internet Connection
```bash
ping google.com
```

### 3. Try Different Network
- Use a different internet connection (mobile hotspot)
- Try from a different location

### 4. Clear Convex Cache
```bash
npx convex logout
npx convex login
```

### 5. Retry Deployment
```bash
npx convex dev --once
```

### 6. Use Verbose Mode
```bash
npx convex dev --once --verbose
```

## Making Users Admins

### Via Admin Panel (Recommended)
1. Log in as a super admin
2. Navigate to http://localhost:5173/admin/users
3. Use the User Management form to update roles

### Via Convex Dashboard
1. Open the Convex dashboard
2. Run the updateUserRole mutation:
```graphql
mutation {
  users:updateUserRole(
    email: "<EMAIL>"
    newRole: "super_admin"
  )
}
```

## Checking User Roles
Run this query in the Convex dashboard:
```graphql
query {
  users:getCurrentUser {
    role
    tenantId
  }
}
```

## Common Issues & Solutions

### User Metadata Not Found
The updateUserRole function now handles this by creating metadata if it doesn't exist.

### Tenant Association Required
Super admins don't need tenant association, but area admins do.

### Network Errors
Retry deployment multiple times or try during off-peak hours.

## Next Steps

1. Try deploying again when network is more stable
2. Test admin functionality locally
3. Verify user roles through the Convex dashboard