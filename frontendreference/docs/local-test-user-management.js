// local-test-user-management.js
console.log("=== Hyperlocal Bangladesh App - User Management ===");
console.log("");
console.log("To manage users in your Hyperlocal Bangladesh app:");
console.log("");
console.log("1. First, ensure you're logged in as an admin user");
console.log("2. Navigate to http://localhost:5173/admin/users in your browser");
console.log("3. Use the User Management form to update user roles");
console.log("");
console.log("For manual testing in Convex dashboard:");
console.log("");
console.log("<NAME_EMAIL> a super admin:");
console.log("Run this mutation in the Convex dashboard:");
console.log("");
console.log("```");
console.log("mutation {");
console.log("  users:updateUserRole(");
console.log("    email: \"<EMAIL>\"");
console.log("    newRole: \"super_admin\"");
console.log("  )");
console.log("}");
console.log("```");
console.log("");
console.log("To check a user's current role:");
console.log("Run this query in the Convex dashboard:");
console.log("");
console.log("```");
console.log("query {");
console.log("  users:getCurrentUser {");
console.log("    role");
console.log("    tenantId");
console.log("  }");
console.log("}");
console.log("```");
console.log("");
console.log("Current issues:");
console.log("- Network errors preventing Convex deployment");
console.log("- User metadata may not exist for new users");
console.log("");
console.log("Solutions:");
console.log("1. The updateUserRole function now creates metadata if it doesn't exist");
console.log("2. For network issues, try:");
console.log("   - Check your internet connection");
console.log("   - Try again later when network is more stable");
console.log("   - Check https://status.convex.dev for service issues");