# Hyperlocal Bangladesh App - Database Seeding Instructions

## Overview
This document provides instructions on how to seed your Hyperlocal Bangladesh app with MVP data.

## Prerequisites
1. Make sure your Convex development server is running (`npx convex dev`)
2. Make sure your frontend development server is running (`npm run dev`)

## Seeding the Database

### Method 1: Using Convex CLI (Recommended)
1. Open a terminal in your project directory
2. Run the following command:
   ```
   npx convex run seedMutations:seedDatabase '{}'
   ```

### Method 2: Using Convex Dashboard
1. Run `npx convex dashboard` to open the Convex dashboard
2. Navigate to the "Functions" tab
3. Find "seedMutations:seedDatabase" in the list
4. Click the "Run" button
5. Pass an empty object `{}` as the argument
6. Click "Run Function"

### Method 3: Manual Seeding via Admin Dashboard
If the automated seeding doesn't work, you can manually create the data through the admin dashboard:
1. Navigate to http://localhost:5173/admin
2. Sign in with your admin account
3. Create the following categories:
   - Emergency Services (জরুরী служба)
   - Government Offices (সরকারী অফিস)
   - Utilities (উপযোগিতা)
   - Historical & Religious Sites (ঐতিহাসিক ও ধর্মীয় স্থান)
   - Public Places (পাবলিক প্লেস)
4. Add the sample listings from the `populate-mvp-data.js` file
5. Create a "Community Updates" listing and add metadata entries

## What the Seeding Process Creates
- 1 Tenant: Alfadanga
- 5 Categories: Emergency Services, Government Offices, Utilities, Historical & Religious Sites, Public Places
- 12 Listings:
  - Alfadanga Police Station
  - Alfadanga Fire Service
  - Upazila Health Complex
  - Alfadanga Upazila Parishad
  - Alfadanga Land Office
  - Alfadanga Palli Bidyut Office
  - Alfadanga Water Supply Authority
  - Ancient Mosque of Alfadanga
  - Local Martyrs' Memorial
  - Alfadanga Central Park
  - Alfadanga Public Library
  - Community Updates
- 1 Review
- 3 Market Price entries

## Troubleshooting
If you encounter network errors when running the seed command:
1. Make sure both Convex and frontend servers are running
2. Check that you can access http://127.0.0.1:3210 in your browser
3. Try restarting both servers
4. If problems persist, use the manual seeding method via the admin dashboard

## Next Steps After Seeding
1. Navigate to http://localhost:5173/admin
2. Sign in with your admin account
3. Browse the categories and listings
4. Test the Community Updates feature
5. Add more listings to reach 100-150 total
6. Test the app thoroughly
7. Prepare for community launch