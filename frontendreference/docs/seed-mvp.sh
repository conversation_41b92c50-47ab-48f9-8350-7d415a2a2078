#!/bin/bash
# seed-mvp.sh
# Script to seed the Hyperlocal Bangladesh app with MVP data

echo "=== Seeding Hyperlocal Bangladesh App with MVP Data ==="
echo ""

echo "Running seed mutation..."
npx convex run seedMutations:seedDatabase '{}'

if [ $? -eq 0 ]; then
  echo ""
  echo "✓ Seed database mutation completed successfully!"
  echo ""
  echo "Your Hyperlocal Bangladesh app is now populated with MVP data:"
  echo "- 1 Tenant: Alfadanga"
  echo "- 5 Categories: Emergency Services, Government Offices, Utilities, Historical & Religious Sites, Public Places"
  echo "- 12 Listings: Police Station, Fire Service, Health Complex, Upazila Parishad, Land Office, Palli Bidyut, Water Supply, Ancient Mosque, Martyrs' Memorial, Central Park, Public Library, Community Updates"
  echo "- 1 Review"
  echo "- 3 Market Price entries"
  echo ""
  echo "Next steps:"
  echo "1. Navigate to http://localhost:5173/admin"
  echo "2. Sign in with your admin account"
  echo "3. Browse the categories and listings"
  echo "4. Test the Community Updates feature"
  echo "5. Add more listings to reach 100-150 total"
else
  echo ""
  echo "✗ Error running seed database mutation"
  echo "Please make sure your Convex development server is running"
fi