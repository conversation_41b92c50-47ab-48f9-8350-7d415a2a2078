// direct-data-population.js
// <PERSON><PERSON><PERSON> to directly populate the Hyperlocal Bangladesh app database with curated MVP data

// Note: This script is for demonstration purposes. You would need to run this in a Convex action/mutation.

console.log("=== Hyperlocal Bangladesh App - Direct Data Population ===\n");

console.log("To directly populate your database with the MVP data, you can use Convex mutations.");
console.log("Here's how you would structure these mutations in your Convex functions:\n");

console.log("// 1. Create categories");
console.log(`
mutation {
  // Example for creating a category
  categories:create({
    tenantId: "your-tenant-id",
    name_bn: "জরুরী служба",
    name_en: "Emergency Services",
    isActive: true
  })
}
`);

console.log("\n// 2. Create listings");
console.log(`
mutation {
  // Example for creating a listing
  listings:submitBusiness({
    tenantId: "your-tenant-id",
    categoryId: "category-id-from-step-1",
    name_bn: "আলফাডাঙ্গা থানা",
    name_en: "Alfadanga Police Station",
    description_bn: "আলফাডাঙ্গা উপজেলার সেবাদানকারী স্থানীয় পুলিশ স্টেশন",
    description_en: "Local police station serving Alfadanga Upazila",
    address: "Alfadanga, Faridpur",
    phone: "***********",
    coordinates: {
      lat: 23.75,
      lng: 90.4
    }
  })
}
`);

console.log("\n// 3. Add metadata to listings");
console.log(`
mutation {
  // Example for adding metadata to a listing
  listingMetadata:create({
    listingId: "listing-id-from-step-2",
    key: "power_outage_jan_25",
    value: "{\\"date\\": \\"2024-01-25\\", \\"area\\": \\"Ward 5, 6\\", \\"time\\": \\"10:00 AM - 2:00 PM\\"}"
  })
}
`);

console.log("\n=== Alternative Approach ===");
console.log("If you prefer to use the admin dashboard (recommended for MVP):");
console.log("1. Navigate to http://localhost:5173/admin");
console.log("2. Sign in with your admin account");
console.log("3. Go to the Listings section");
console.log("4. Click 'Add New Listing'");
console.log("5. Fill in the form with the sample data provided in the previous script");
console.log("6. Set the status to 'approved' and isVerified to 'true'");
console.log("7. Repeat for all 100-150 listings");

console.log("\n=== Pro Tips ===");
console.log("1. Use Excel or Google Sheets to organize your data before inputting");
console.log("2. Copy and paste descriptions to save time");
console.log("3. Use placeholder images for now (you can update them later)");
console.log("4. Focus on getting 20-30 high-quality listings first");
console.log("5. Test the app thoroughly with your initial data");
console.log("6. Get feedback from someone in the Alfadanga community");