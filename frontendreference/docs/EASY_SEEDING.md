# Easy Ways to Seed Your Hyperlocal Bangladesh App

## Overview
This document explains the simple, no-code ways to populate your Hyperlocal Bangladesh app with data for the MVP launch.

## Method 1: One-Click Seed Button (Easiest)

### What it does:
- Creates 1 tenant (Alfadanga)
- Creates 5 categories (Emergency Services, Government Offices, Utilities, Historical & Religious Sites, Public Places)
- Creates 12 key listings including:
  - Police Station, Fire Service, Health Complex
  - Upazila Parishad, Land Office
  - Palli Bidyut Office, Water Supply Authority
  - Ancient Mosque, Martyrs' Memorial
  - Central Park, Public Library
  - Community Updates listing with sample metadata
- Creates sample reviews and market prices

### How to use:
1. Start your development servers:
   ```
   npx convex dev     # Backend
   npm run dev        # Frontend
   ```
2. Navigate to http://localhost:5173/admin
3. Sign in with your admin account
4. On the dashboard homepage, click the "Seed Database" button
5. Wait for the success message

## Method 2: CSV Upload

### What you need:
- A CSV file with your listing data
- Sample provided: `mvp-data.csv`

### CSV Format:
```
category_name_bn,category_name_en,listing_name_bn,listing_name_en,description_bn,description_en,address,phone,website,latitude,longitude
```

### How to use:
1. Start your development servers
2. Navigate to http://localhost:5173/admin
3. Sign in with your admin account
4. On the dashboard homepage, click "Select CSV File" in the CSV Upload section
5. Choose your CSV file
6. Wait for processing to complete

## Method 3: Manual Entry

### How to use:
1. Start your development servers
2. Navigate to http://localhost:5173/admin
3. Sign in with your admin account
4. Go to the "Listings" section
5. Click "Add New Listing"
6. Fill in the form and save

## Method 4: Command Line (For Developers)

### How to use:
1. Start your development servers
2. In a terminal, run:
   ```
   npm run seed
   ```
   OR
   ```
   npx convex run seedMutations:seedDatabase '{}'
   ```

## Recommendations for MVP Launch

1. **Start with Method 1 (Seed Button)**: Get sample data instantly to see how the app works
2. **Add Your Own Data**: Use Method 2 (CSV Upload) or Method 3 (Manual Entry) to add real data for Alfadanga
3. **Focus on Quality**: 20-30 high-quality listings are better than 100 poor ones
4. **Include Key Categories**: Make sure you have listings in all 5 key categories
5. **Test Thoroughly**: Check that all features work with your data

## Next Steps After Seeding

1. Add more listings to reach 100-150 total
2. Customize the Community Updates listing with real local information
3. Add photos to listings when possible
4. Test the app on both desktop and mobile
5. Get feedback from someone in the Alfadanga community
6. Prepare for community launch

## Troubleshooting

### If the Seed Button doesn't work:
- Make sure both Convex and frontend servers are running
- Check browser console for errors
- Try the command line method instead

### If CSV Upload doesn't work:
- Make sure your CSV follows the exact format
- Check that you're uploading a .csv file
- Look for error messages in the upload status area

### If you see network errors:
- Restart both development servers
- Check that you can access http://127.0.0.1:3210 in your browser
- Make sure you're not behind a firewall that blocks localhost connections