# Project Overview

This is a full-stack web application built with the following technologies:

*   **Frontend:** React, Vite, TypeScript, Tailwind CSS
*   **Backend:** Convex (database, serverless functions)
*   **Authentication:** Clerk

The application provides a simple interface to display a list of numbers and add new numbers to the list. It uses <PERSON> for user authentication and Convex for data persistence and backend logic.

## Building and Running

To build and run the project, you need to have Node.js and npm installed.

1.  **Install dependencies:**

    ```bash
    npm install
    ```

2.  **Run the development server:**

    ```bash
    npm run dev
    ```

    This will start both the frontend and backend development servers.

3.  **Build for production:**

    ```bash
    npm run build
    ```

## Development Conventions

*   **Code Style:** The project uses <PERSON><PERSON><PERSON> for code formatting and ESLint for linting.
*   **Testing:** There are no testing frameworks configured in the project.
*   **File Structure:**
    *   `src`: Contains the frontend source code.
    *   `convex`: Contains the backend source code (schema, functions).
    *   `public`: Contains public assets.
