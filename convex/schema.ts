import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  // 1. Users (Simplified)
  user_profiles: defineTable({
    clerkUserId: v.string(),
    stableUserId: v.string(),
    identity: v.object({
      name: v.object({ bn: v.string(), en: v.optional(v.string()) }),
    }),
    contact: v.object({ phone: v.optional(v.string()) }),
    preferences: v.object({
      language: v.union(v.literal("bn"), v.literal("en")),
      theme: v.union(v.literal("light"), v.literal("dark"), v.literal("auto")),
    }),
    role: v.union(
      v.literal("user"),
      v.literal("business_owner"),
      v.literal("admin"),
    ),
    isOnboarded: v.boolean(),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_clerk_user_id", ["clerkUserId"])
    .index("by_stable_user_id", ["stableUserId"]),

  // 2. Locations (Essential for your directory)
  locations: defineTable({
    name: v.object({ bn: v.string(), en: v.string() }),
    slug: v.string(),
    type: v.union(
      v.literal("division"),
      v.literal("district"),
      v.literal("upazila"),
    ),
    parentId: v.optional(v.id("locations")),
    isActive: v.boolean(),
    createdAt: v.number(),
  })
    .index("by_slug", ["slug"])
    .index("by_parent", ["parentId"]),

  // 3. DIRECTORY TABLES
  categories: defineTable({
    name: v.object({ bn: v.string(), en: v.string() }),
    slug: v.string(),
    isActive: v.boolean(),
  }).index("by_slug", ["slug"]),

  listings: defineTable({
    ownerId: v.id("user_profiles"),
    categoryId: v.id("categories"),
    locationId: v.id("locations"),
    name: v.object({ bn: v.string(), en: v.optional(v.string()) }),
    contact: v.object({ phone: v.string() }),
    status: v.union(
      v.literal("draft"),
      v.literal("pending_review"),
      v.literal("published"),
      v.literal("rejected"),
    ),
    // Add new fields for enhanced search
    keywords: v.optional(v.array(v.string())),
    isFeatured: v.optional(v.boolean()),
    viewCount: v.optional(v.number()),
    clickCount: v.optional(v.number()),
    moderationNotes: v.optional(v.string()),
    publishedAt: v.optional(v.number()),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_owner", ["ownerId"])
    .index("by_location", ["locationId"])
    .index("by_status", ["status"])
    .index("by_status_creation", ["status", "createdAt"])
    .index("by_category", ["categoryId"])
    // Add new indexes for enhanced search performance
    .index("by_location_status", ["locationId", "status"])
    .index("by_category_status", ["categoryId", "status"])
    .index("by_featured_created", ["isFeatured", "_creationTime"]),

  // 4. Analytics table for tracking listing interactions
  listing_analytics: defineTable({
    listingId: v.id("listings"),
    userId: v.optional(v.string()),
    action: v.string(),
    timestamp: v.number(),
    userAgent: v.optional(v.string()),
  })
    .index("by_listing", ["listingId"])
    .index("by_user", ["userId"])
    .index("by_action", ["action"]),

  // 5. Audit Logs (for tracking actions)
  audit_logs: defineTable({
    userId: v.id("user_profiles"),
    action: v.string(),
    targetId: v.optional(v.string()),
    details: v.optional(v.any()),
    createdAt: v.number(),
  }).index("by_user_id", ["userId"]),
});
