// convex/directory/categories.ts
import { query, mutation } from "../_generated/server";
import { v } from "convex/values";

// 1. List Categories
export const listCategories = query({
  args: {
    isActive: v.optional(v.boolean()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    let query = ctx.db.query("categories");

    if (args.isActive !== undefined) {
      query = query.filter((q) => q.eq(q.field("isActive"), args.isActive));
    }

    const results = await query.order("asc").take(args.limit || 100);

    return results;
  },
});

// 2. Get Category by ID
export const getCategory = query({
  args: { categoryId: v.id("categories") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.categoryId);
  },
});

// 3. Create Category (admin only)
export const createCategory = mutation({
  args: {
    name: v.object({
      bn: v.string(),
      en: v.string(),
    }),
    slug: v.string(),
    isActive: v.boolean(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.db
      .query("user_profiles")
      .withIndex("by_clerk_user_id", (q) =>
        q.eq("clerkUserId", identity.subject),
      )
      .unique();

    if (!user) throw new Error("User not found");
    if (user.role !== "admin") throw new Error("Admin access required");

    // Check for duplicate slug
    const existing = await ctx.db
      .query("categories")
      .withIndex("by_slug", (q) => q.eq("slug", args.slug))
      .unique();

    if (existing) throw new Error("Category with this slug already exists");

    const now = Date.now();

    const categoryId = await ctx.db.insert("categories", {
      name: args.name,
      slug: args.slug,
      isActive: args.isActive,
    });

    // Log the action
    await ctx.db.insert("audit_logs", {
      userId: user._id,
      action: "category:created",
      targetId: categoryId,
      details: {
        name: args.name,
        slug: args.slug,
      },
      createdAt: now,
    });

    return categoryId;
  },
});

// 4. Update Category (admin only)
export const updateCategory = mutation({
  args: {
    categoryId: v.id("categories"),
    updates: v.object({
      name: v.optional(
        v.object({
          bn: v.string(),
          en: v.string(),
        }),
      ),
      slug: v.optional(v.string()),
      isActive: v.optional(v.boolean()),
    }),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.db
      .query("user_profiles")
      .withIndex("by_clerk_user_id", (q) =>
        q.eq("clerkUserId", identity.subject),
      )
      .unique();

    if (!user) throw new Error("User not found");
    if (user.role !== "admin") throw new Error("Admin access required");

    // Check for duplicate slug if slug is being updated
    if (args.updates.slug) {
      const existing = await ctx.db
        .query("categories")
        .withIndex("by_slug", (q: any) => q.eq("slug", args.updates.slug!))
        .filter((q: any) => q.neq(q.field("_id"), args.categoryId))
        .unique();

      if (existing) throw new Error("Category with this slug already exists");
    }

    const now = Date.now();

    await ctx.db.patch(args.categoryId, {
      ...args.updates,
    });

    // Log the action
    await ctx.db.insert("audit_logs", {
      userId: user._id,
      action: "category:updated",
      targetId: args.categoryId,
      details: {
        updates: args.updates,
      },
      createdAt: now,
    });

    return { success: true };
  },
});
