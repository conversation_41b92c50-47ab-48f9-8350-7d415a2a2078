// convex/directory/schema.ts
// Note: This schema is already defined in the main schema.ts file
// This file is created for organizational purposes but the tables are defined in the main schema

// The following tables are already defined in the main schema.ts:
// 1. categories (in main schema)
// 2. listings (in main schema)
// 3. reviews (not yet in main schema - would need to be added to main schema to use)

// For this implementation, we'll work with the existing schema which includes:
// - categories
// - listings
// - user_profiles
// - locations
// - audit_logs

export default {};
