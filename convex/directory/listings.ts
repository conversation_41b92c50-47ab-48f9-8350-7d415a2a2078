// convex/directory/listings.ts
import { query, mutation } from "../_generated/server";
import { v } from "convex/values";

// 1. Create Listing
export const createListing = mutation({
  args: {
    categoryId: v.id("categories"),
    name: v.object({
      bn: v.string(),
      en: v.optional(v.string()),
    }),
    phone: v.string(),
    locationId: v.id("locations"),
    keywords: v.optional(v.array(v.string())),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.db
      .query("user_profiles")
      .withIndex("by_clerk_user_id", (q) =>
        q.eq("clerkUserId", identity.subject),
      )
      .unique();

    if (!user) throw new Error("User not found");

    const now = Date.now();

    return await ctx.db.insert("listings", {
      ownerId: user._id,
      categoryId: args.categoryId,
      name: args.name,
      contact: { phone: args.phone },
      locationId: args.locationId,
      keywords: args.keywords,
      isFeatured: false,
      viewCount: 0,
      clickCount: 0,
      status: "draft",
      createdAt: now,
      updatedAt: now,
    });
  },
});

// 2. Get Listing by ID
export const getListing = query({
  args: { listingId: v.id("listings") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.listingId);
  },
});

// 3. List Listings by Owner
export const listByOwner = query({
  args: {
    ownerId: v.id("user_profiles"),
    status: v.optional(v.union(v.literal("draft"), v.literal("published"))),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const query = ctx.db
      .query("listings")
      .withIndex("by_owner", (q) => q.eq("ownerId", args.ownerId));

    if (args.status) {
      query.filter((q) => q.eq(q.field("status"), args.status));
    }

    const results = await query.order("desc").take(args.limit || 50);

    return results;
  },
});

// 4. List Listings by Location
export const listByLocation = query({
  args: {
    locationId: v.id("locations"),
    status: v.optional(v.union(v.literal("draft"), v.literal("published"))),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const query = ctx.db
      .query("listings")
      .withIndex("by_location_status", (q) =>
        q
          .eq("locationId", args.locationId)
          .eq("status", args.status || "published"),
      );

    const results = await query.order("desc").take(args.limit || 50);

    return results;
  },
});

// 5. List Listings by Category
export const listByCategory = query({
  args: {
    categoryId: v.id("categories"),
    status: v.optional(v.union(v.literal("draft"), v.literal("published"))),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const query = ctx.db
      .query("listings")
      .withIndex("by_category_status", (q) =>
        q
          .eq("categoryId", args.categoryId)
          .eq("status", args.status || "published"),
      );

    const results = await query.order("desc").take(args.limit || 50);

    return results;
  },
});

// 6. Update Listing
export const updateListing = mutation({
  args: {
    listingId: v.id("listings"),
    updates: v.object({
      name: v.optional(
        v.object({
          bn: v.string(),
          en: v.optional(v.string()),
        }),
      ),
      categoryId: v.optional(v.id("categories")),
      contact: v.optional(
        v.object({
          phone: v.string(),
        }),
      ),
      locationId: v.optional(v.id("locations")),
      keywords: v.optional(v.array(v.string())),
      isFeatured: v.optional(v.boolean()),
      status: v.optional(
        v.union(
          v.literal("draft"),
          v.literal("pending_review"),
          v.literal("published"),
          v.literal("rejected"),
        ),
      ),
    }),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.db
      .query("user_profiles")
      .withIndex("by_clerk_user_id", (q) =>
        q.eq("clerkUserId", identity.subject),
      )
      .unique();

    if (!user) throw new Error("User not found");

    // Get the listing to verify ownership
    const listing = await ctx.db.get(args.listingId);
    if (!listing) throw new Error("Listing not found");
    if (listing.ownerId !== user._id && user.role !== "admin") {
      throw new Error("Not authorized to update this listing");
    }

    const now = Date.now();

    await ctx.db.patch(args.listingId, {
      ...args.updates,
      updatedAt: now,
    });

    // Log the action
    await ctx.db.insert("audit_logs", {
      userId: user._id,
      action: "listing:updated",
      targetId: args.listingId,
      details: {
        updates: args.updates,
      },
      createdAt: now,
    });

    return { success: true };
  },
});

// 7. Delete Listing
export const deleteListing = mutation({
  args: { listingId: v.id("listings") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.db
      .query("user_profiles")
      .withIndex("by_clerk_user_id", (q) =>
        q.eq("clerkUserId", identity.subject),
      )
      .unique();

    if (!user) throw new Error("User not found");

    // Get the listing to verify ownership
    const listing = await ctx.db.get(args.listingId);
    if (!listing) throw new Error("Listing not found");
    if (listing.ownerId !== user._id && user.role !== "admin") {
      throw new Error("Not authorized to delete this listing");
    }

    await ctx.db.delete(args.listingId);

    // Log the action
    await ctx.db.insert("audit_logs", {
      userId: user._id,
      action: "listing:deleted",
      targetId: args.listingId,
      createdAt: Date.now(),
    });

    return { success: true };
  },
});

// 8. Publish Listing
export const publishListing = mutation({
  args: { listingId: v.id("listings") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.db
      .query("user_profiles")
      .withIndex("by_clerk_user_id", (q) =>
        q.eq("clerkUserId", identity.subject),
      )
      .unique();

    if (!user) throw new Error("User not found");

    // Get the listing to verify ownership
    const listing = await ctx.db.get(args.listingId);
    if (!listing) throw new Error("Listing not found");
    if (listing.ownerId !== user._id && user.role !== "admin") {
      throw new Error("Not authorized to publish this listing");
    }

    const now = Date.now();

    await ctx.db.patch(args.listingId, {
      status: "published",
      publishedAt: now,
      updatedAt: now,
    });

    // Log the action
    await ctx.db.insert("audit_logs", {
      userId: user._id,
      action: "listing:published",
      targetId: args.listingId,
      createdAt: now,
    });

    return { success: true };
  },
});

// 9. Feature Listing (Admin only)
export const featureListing = mutation({
  args: {
    listingId: v.id("listings"),
    isFeatured: v.boolean(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.db
      .query("user_profiles")
      .withIndex("by_clerk_user_id", (q) =>
        q.eq("clerkUserId", identity.subject),
      )
      .unique();

    if (!user) throw new Error("User not found");
    if (user.role !== "admin") {
      throw new Error("Admin access required");
    }

    // Get the listing
    const listing = await ctx.db.get(args.listingId);
    if (!listing) throw new Error("Listing not found");

    const now = Date.now();

    await ctx.db.patch(args.listingId, {
      isFeatured: args.isFeatured,
      updatedAt: now,
    });

    // Log the action
    await ctx.db.insert("audit_logs", {
      userId: user._id,
      action: "listing:featured",
      targetId: args.listingId,
      details: {
        isFeatured: args.isFeatured,
      },
      createdAt: now,
    });

    return { success: true };
  },
});
