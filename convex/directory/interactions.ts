// convex/directory/interactions.ts
import { mutation, query } from "../_generated/server";
import { v } from "convex/values";

export const recordClick = mutation({
  args: { listingId: v.id("listings") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();

    const listing = await ctx.db.get(args.listingId);
    if (!listing || listing.status !== "published") {
      throw new Error("Invalid listing");
    }

    // Update click count
    await ctx.db.patch(args.listingId, {
      clickCount: (listing.clickCount || 0) + 1,
    });

    // Record detailed analytics if user is logged in
    if (identity) {
      await ctx.db.insert("listing_analytics", {
        listingId: args.listingId,
        userId: identity.subject,
        action: "click",
        timestamp: Date.now(),
        userAgent: "unknown",
      });
    }

    return { success: true };
  },
});

export const getSimilarListings = query({
  args: {
    listingId: v.id("listings"),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const currentListing = await ctx.db.get(args.listingId);
    if (!currentListing) return [];

    return await ctx.db
      .query("listings")
      .withIndex("by_category_status", (q) =>
        q.eq("categoryId", currentListing.categoryId).eq("status", "published"),
      )
      .filter((q) => q.neq(q.field("_id"), args.listingId))
      .order("desc")
      .take(args.limit || 6);
  },
});

export const getViewCount = query({
  args: { listingId: v.id("listings") },
  handler: async (ctx, args) => {
    const listing = await ctx.db.get(args.listingId);
    return listing ? listing.viewCount || 0 : 0;
  },
});

export const getClickCount = query({
  args: { listingId: v.id("listings") },
  handler: async (ctx, args) => {
    const listing = await ctx.db.get(args.listingId);
    return listing ? listing.clickCount || 0 : 0;
  },
});
