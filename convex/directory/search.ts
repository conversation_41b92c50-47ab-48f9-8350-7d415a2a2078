// convex/directory/search.ts
import { query } from "../_generated/server";
import { v } from "convex/values";

export const searchListings = query({
  args: {
    searchQuery: v.optional(v.string()),
    categoryId: v.optional(v.id("categories")),
    locationId: v.optional(v.id("locations")),
    isFeatured: v.optional(v.boolean()),
    status: v.optional(
      v.union(
        v.literal("draft"),
        v.literal("published"),
        v.literal("pending_review"),
        v.literal("rejected"),
      ),
    ),
    sortBy: v.optional(
      v.union(v.literal("newest"), v.literal("featured"), v.literal("name")),
    ),
    limit: v.optional(v.number()),
    cursor: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 20;

    // Start with base query for listings
    let queryBuilder = ctx.db.query("listings");

    // 🔥 CRITICAL: Use the most specific index first for performance
    if (args.locationId && args.categoryId) {
      // Most specific: both location and category
      queryBuilder = queryBuilder.withIndex("by_location_status", (q) =>
        q
          .eq("locationId", args.locationId)
          .eq("status", args.status || "published"),
      );
    } else if (args.locationId) {
      // Specific: location only
      queryBuilder = queryBuilder.withIndex("by_location_status", (q) =>
        q
          .eq("locationId", args.locationId)
          .eq("status", args.status || "published"),
      );
    } else if (args.categoryId) {
      // Specific: category only
      queryBuilder = queryBuilder.withIndex("by_category_status", (q) =>
        q
          .eq("categoryId", args.categoryId)
          .eq("status", args.status || "published"),
      );
    } else {
      // General: status only
      queryBuilder = queryBuilder.withIndex("by_status", (q) =>
        q.eq("status", args.status || "published"),
      );
    }

    // Apply filters
    if (args.searchQuery) {
      const searchTerm = args.searchQuery.toLowerCase();
      queryBuilder = queryBuilder.filter((q) =>
        q.or(
          q.search("name.bn", searchTerm),
          q.search("name.en", searchTerm),
          q.eq("keywords", searchTerm),
        ),
      );
    }

    if (args.isFeatured !== undefined) {
      queryBuilder = queryBuilder.filter((q) =>
        q.eq(q.field("isFeatured"), args.isFeatured),
      );
    }

    // Apply sorting
    switch (args.sortBy) {
      case "featured":
        queryBuilder = queryBuilder.order("desc");
        break;
      case "newest":
        queryBuilder = queryBuilder.order("desc");
        break;
      case "name":
      default:
        queryBuilder = queryBuilder.order("asc");
    }

    // Execute query with pagination
    const results = await queryBuilder.take(limit + 1);

    const hasMore = results.length > limit;
    const items = results.slice(0, limit);
    const nextCursor = hasMore ? items[items.length - 1]?._id : undefined;

    return {
      items,
      nextCursor,
      hasMore,
    };
  },
});

export const getListingDetail = query({
  args: { listingId: v.id("listings") },
  handler: async (ctx, args) => {
    const listing = await ctx.db.get(args.listingId);

    if (!listing || listing.status !== "published") {
      throw new Error("Listing not found");
    }

    // Increment view count (optimistic update)
    ctx.db
      .patch(args.listingId, {
        viewCount: (listing.viewCount || 0) + 1,
      })
      .catch(console.error); // Non-blocking

    // Get related data
    const category = await ctx.db.get(listing.categoryId);
    const location = await ctx.db.get(listing.locationId);
    const owner = await ctx.db.get(listing.ownerId);

    return {
      ...listing,
      category: category
        ? {
            _id: category._id,
            name: category.name,
            slug: category.slug,
          }
        : null,
      location: location
        ? {
            _id: location._id,
            name: location.name,
            slug: location.slug,
          }
        : null,
      owner: owner
        ? {
            _id: owner._id,
            name: owner.identity.name,
            isOnboarded: owner.isOnboarded,
          }
        : null,
    };
  },
});

export const getFeaturedListings = query({
  args: { limit: v.optional(v.number()) },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("listings")
      .withIndex("by_featured_created", (q) =>
        q.eq("isFeatured", true).eq("status", "published"),
      )
      .order("desc")
      .take(args.limit || 10);
  },
});

// 2. Get Listings with Category and Location Info (for display)
export const getListingsWithDetails = query({
  args: {
    categoryId: v.optional(v.id("categories")),
    locationId: v.optional(v.id("locations")),
    status: v.optional(v.union(v.literal("draft"), v.literal("published"))),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    // First get the listings
    let query = ctx.db.query("listings");

    // Filter by status
    if (args.status) {
      query = query.filter((q) => q.eq(q.field("status"), args.status));
    } else {
      // Default to published listings
      query = query.filter((q) => q.eq(q.field("status"), "published"));
    }

    // Filter by category
    if (args.categoryId) {
      query = query.filter((q) => q.eq(q.field("categoryId"), args.categoryId));
    }

    // Filter by location
    if (args.locationId) {
      query = query.filter((q) => q.eq(q.field("locationId"), args.locationId));
    }

    const listings = await query.order("desc").take(args.limit || 20);

    // Get related data for each listing
    const listingsWithDetails = await Promise.all(
      listings.map(async (listing) => {
        // Get category info
        const category = await ctx.db.get(listing.categoryId);

        // Get location info
        const location = await ctx.db.get(listing.locationId);

        // Get owner info
        const owner = await ctx.db.get(listing.ownerId);

        return {
          ...listing,
          category: category
            ? { name: category.name, slug: category.slug }
            : null,
          location: location
            ? { name: location.name, slug: location.slug }
            : null,
          owner: owner
            ? {
                name: owner.identity.name,
                isOnboarded: owner.isOnboarded,
              }
            : null,
        };
      }),
    );

    return listingsWithDetails;
  },
});
