import { query } from "../_generated/server";

export const debugDatabaseInfo = query({
  handler: async (ctx) => {
    // Check all tables
    const userProfiles = await ctx.db.query("user_profiles").collect();
    const locations = await ctx.db.query("locations").collect();
    const categories = await ctx.db.query("categories").collect();
    const listings = await ctx.db.query("listings").collect();
    const auditLogs = await ctx.db.query("audit_logs").collect();
    
    return {
      tables: {
        user_profiles: {
          count: userProfiles.length,
          sample: userProfiles.slice(0, 3)
        },
        locations: {
          count: locations.length,
          sample: locations.slice(0, 3)
        },
        categories: {
          count: categories.length,
          sample: categories.slice(0, 3)
        },
        listings: {
          count: listings.length,
          sample: listings.slice(0, 3)
        },
        audit_logs: {
          count: auditLogs.length,
          sample: auditLogs.slice(0, 3)
        }
      }
    };
  },
});