import { query } from "../_generated/server";

export const debugListUsersNoAuth = query({
  handler: async (ctx) => {
    const users = await ctx.db.query("user_profiles").collect();
    return users.map(user => ({
      id: user._id,
      clerkUserId: user.clerkUserId,
      name: user.identity.name,
      role: user.role,
      isOnboarded: user.isOnboarded,
      createdAt: new Date(user.createdAt).toISOString(),
    }));
  },
});