import { mutation } from "../_generated/server";
import { v } from "convex/values";

export const debugPromoteToAdmin = mutation({
  args: {
    userId: v.string(),
  },
  handler: async (ctx, args) => {
    // Get the user to promote
    const userToPromote = await ctx.db.get(args.userId as any);
    if (!userToPromote) {
      throw new Error("User not found");
    }

    // Update the user's role
    await ctx.db.patch(args.userId as any, {
      role: "admin",
      updatedAt: Date.now(),
    });

    return { success: true, message: `User ${args.userId} promoted to admin` };
  },
});