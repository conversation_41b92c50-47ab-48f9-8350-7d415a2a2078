/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as core_admin from "../core/admin.js";
import type * as core_errors from "../core/errors.js";
import type * as core_locations from "../core/locations.js";
import type * as core_users from "../core/users.js";
import type * as core_validation from "../core/validation.js";
import type * as core_verify_utilities from "../core/verify-utilities.js";
import type * as debug_databaseInfo from "../debug/databaseInfo.js";
import type * as debug_listUsersNoAuth from "../debug/listUsersNoAuth.js";
import type * as debug_populateDatabase from "../debug/populateDatabase.js";
import type * as debug_promoteToAdmin from "../debug/promoteToAdmin.js";
import type * as directory_categories from "../directory/categories.js";
import type * as directory_index from "../directory/index.js";
import type * as directory_interactions from "../directory/interactions.js";
import type * as directory_listings from "../directory/listings.js";
import type * as directory_search from "../directory/search.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  "core/admin": typeof core_admin;
  "core/errors": typeof core_errors;
  "core/locations": typeof core_locations;
  "core/users": typeof core_users;
  "core/validation": typeof core_validation;
  "core/verify-utilities": typeof core_verify_utilities;
  "debug/databaseInfo": typeof debug_databaseInfo;
  "debug/listUsersNoAuth": typeof debug_listUsersNoAuth;
  "debug/populateDatabase": typeof debug_populateDatabase;
  "debug/promoteToAdmin": typeof debug_promoteToAdmin;
  "directory/categories": typeof directory_categories;
  "directory/index": typeof directory_index;
  "directory/interactions": typeof directory_interactions;
  "directory/listings": typeof directory_listings;
  "directory/search": typeof directory_search;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
