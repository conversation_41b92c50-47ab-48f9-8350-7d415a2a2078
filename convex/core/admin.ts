// convex/admin.ts
import { query, mutation } from "../_generated/server";
import { v } from "convex/values";

// 1. Centralized Admin Authorization Check
async function checkIsAdmin(ctx: any): Promise<void> {
  const identity = await ctx.auth.getUserIdentity();
  if (!identity) {
    throw new Error("Unauthorized - Not authenticated");
  }

  const user = await ctx.db
    .query("user_profiles")
    .withIndex("by_clerk_user_id", (q: any) => 
      q.eq("clerkUserId", identity?.subject)
    )
    .unique();

  // Explicitly check for admin role
  if (!user || user.role !== "admin") {
    // Log unauthorized access attempt
    console.warn(`Unauthorized admin access attempt by user: ${user?._id}`);
    throw new Error("Unauthorized - Admin access required");
  }
}

// 2. GET PENDING LISTINGS (With Pagination)
export const getPendingListings = query({
  args: {
    limit: v.optional(v.number()),
    cursor: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    await checkIsAdmin(ctx); // Authorization check

    const limit = args.limit || 50;

    let query = ctx.db
      .query("listings")
      .withIndex("by_status_creation", (q) => 
        q.eq("status", "pending_review")
      );

    // Handle pagination if cursor is provided
    if (args.cursor) {
      query = query.filter((q) => q.lt(q.field("createdAt"), parseInt(args.cursor || "0")));
    }

    const listings = await query
      .order("desc")
      .take(limit + 1); // Fetch one extra to check for next page

    const hasMore = listings.length > limit;
    const results = listings.slice(0, limit);
    const nextCursor = hasMore ? results[results.length - 1]?.createdAt.toString() : undefined;

    return {
      results,
      nextCursor,
      hasMore,
    };
  },
});

// 3. APPROVE LISTING (With Audit Trail)
export const approveListing = mutation({
  args: { 
    listingId: v.id("listings"),
    notes: v.optional(v.string()) 
  },
  handler: async (ctx, args) => {
    await checkIsAdmin(ctx);

    const listing = await ctx.db.get(args.listingId);
    if (!listing) {
      throw new Error("Listing not found");
    }

    if (listing.status !== "pending_review") {
      throw new Error("Listing is not pending review");
    }

    await ctx.db.patch(args.listingId, {
      status: "published",
      publishedAt: Date.now(),
      moderationNotes: args.notes, // Store reason for approval
    });

    // ✅ BEST PRACTICE: Create audit log entry
    const identity = await ctx.auth.getUserIdentity();
    const user = await ctx.db
      .query("user_profiles")
      .withIndex("by_clerk_user_id", (q: any) => 
        q.eq("clerkUserId", identity?.subject)
      )
      .unique();
      
    if (!user) {
      throw new Error("User profile not found");
    }
      
    await ctx.db.insert("audit_logs", {
      action: "listing:approved",
      targetId: args.listingId,
      userId: user._id, // Store user profile ID
      details: {
        previousStatus: listing.status,
        notes: args.notes,
      },
      createdAt: Date.now(),
    });

    return { success: true };
  },
});

// 4. REJECT LISTING (With Required Reason)
export const rejectListing = mutation({
  args: { 
    listingId: v.id("listings"),
    reason: v.string(), // ✅ REQUIRED field for rejection
  },
  handler: async (ctx, args) => {
    await checkIsAdmin(ctx);

    const listing = await ctx.db.get(args.listingId);
    if (!listing) {
      throw new Error("Listing not found");
    }

    if (listing.status !== "pending_review") {
      throw new Error("Listing is not pending review");
    }

    await ctx.db.patch(args.listingId, {
      status: "rejected",
      moderationNotes: args.reason, // Store rejection reason
    });

    // ✅ BEST PRACTICE: Audit log
    const identity = await ctx.auth.getUserIdentity();
    const user = await ctx.db
      .query("user_profiles")
      .withIndex("by_clerk_user_id", (q: any) => 
        q.eq("clerkUserId", identity?.subject)
      )
      .unique();
      
    if (!user) {
      throw new Error("User profile not found");
    }
      
    await ctx.db.insert("audit_logs", {
      action: "listing:rejected",
      targetId: args.listingId,
      userId: user._id,
      details: {
        previousStatus: listing.status,
        reason: args.reason,
      },
      createdAt: Date.now(),
    });

    return { success: true };
  },
});

// 5. GET LISTING STATS (For Admin Dashboard)
export const getDashboardStats = query({
  handler: async (ctx) => {
    await checkIsAdmin(ctx);

    const allListings = await ctx.db.query("listings").collect();
    
    return {
      total: allListings.length,
      published: allListings.filter(l => l.status === "published").length,
      pending: allListings.filter(l => l.status === "pending_review").length,
      rejected: allListings.filter(l => l.status === "rejected").length,
      draft: allListings.filter(l => l.status === "draft").length,
    };
  },
});

// 6. GET LISTINGS BY STATUS (For Admin Filtering)
export const getListingsByStatus = query({
  args: {
    status: v.union(
      v.literal("draft"),
      v.literal("pending_review"),
      v.literal("published"),
      v.literal("rejected")
    ),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    await checkIsAdmin(ctx);

    const limit = args.limit || 50;

    const listings = await ctx.db
      .query("listings")
      .withIndex("by_status_creation", (q) => 
        q.eq("status", args.status)
      )
      .order("desc")
      .take(limit);

    return listings;
  },
});

// 7. UPDATE LISTING STATUS (Generic status update)
export const updateListingStatus = mutation({
  args: { 
    listingId: v.id("listings"),
    status: v.union(
      v.literal("draft"),
      v.literal("pending_review"),
      v.literal("published"),
      v.literal("rejected")
    ),
    notes: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    await checkIsAdmin(ctx);

    const listing = await ctx.db.get(args.listingId);
    if (!listing) {
      throw new Error("Listing not found");
    }

    // Prevent invalid status transitions
    if (listing.status === "published" && args.status === "draft") {
      throw new Error("Cannot move published listing back to draft");
    }

    const updates: any = {
      status: args.status,
      moderationNotes: args.notes,
    };

    // Set publishedAt if moving to published
    if (args.status === "published" && !listing.publishedAt) {
      updates.publishedAt = Date.now();
    }

    await ctx.db.patch(args.listingId, updates);

    // ✅ BEST PRACTICE: Audit log
    const identity = await ctx.auth.getUserIdentity();
    const user = await ctx.db
      .query("user_profiles")
      .withIndex("by_clerk_user_id", (q: any) => 
        q.eq("clerkUserId", identity?.subject)
      )
      .unique();
      
    if (!user) {
      throw new Error("User profile not found");
    }
      
    await ctx.db.insert("audit_logs", {
      action: "listing:status_updated",
      targetId: args.listingId,
      userId: user._id,
      details: {
        previousStatus: listing.status,
        newStatus: args.status,
        notes: args.notes,
      },
      createdAt: Date.now(),
    });

    return { success: true };
  },
});