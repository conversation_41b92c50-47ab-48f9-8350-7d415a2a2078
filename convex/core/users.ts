// convex/users.ts
import { query, mutation } from "../_generated/server";
import { v } from "convex/values";
import {
  requireAuth,
  requireAdmin,
  getCurrentUser,
  NotFoundError,
  ValidationError
} from "./errors";
import {
  assert<PERSON><PERSON><PERSON><PERSON><PERSON>,
  assertValidName,
  validateUserUpdates as validateUserUpdatesNew,
  assertRequired
} from "./validation";

// 1. Get Current User (with proper auth handling)
export const getCurrent = query({
  handler: async (ctx) => {
    try {
      const identity = await ctx.auth.getUserIdentity();
      if (!identity) {
        return null;
      }

      const user = await ctx.db
        .query("user_profiles")
        .withIndex("by_clerk_user_id", (q: any) =>
          q.eq("clerkUserId", identity.subject)
        )
        .unique();

      return user;
    } catch (error) {
      console.error("Error in getCurrent:", error);
      throw new NotFoundError("User profile");
    }
  },
});

// 2. Get User by Stable ID (for internal references)
export const getByStableId = query({
  args: {
    stableUserId: v.string(),
  },
  handler: async (ctx, args) => {
    try {
      assertRequired(args.stableUserId, "stableUserId");

      return await ctx.db
        .query("user_profiles")
        .withIndex("by_stable_user_id", (q: any) =>
          q.eq("stableUserId", args.stableUserId)
        )
        .unique();
    } catch (error) {
      console.error("Error in getByStableId:", error);
      if (error instanceof ValidationError) {
        throw error;
      }
      throw new NotFoundError("User profile");
    }
  },
});

// 3. Create User on Sign-Up (with validation and error handling)
export const createOnSignUp = mutation({
  args: {
    name: v.object({
      bn: v.string(),
      en: v.optional(v.string()),
    }),
    phone: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    try {
      // Use the new auth helper
      const identity = await requireAuth(ctx);

      console.log("Creating user with identity:", {
        subject: identity.subject,
        issuer: identity.issuer,
        email: identity.email,
        name: identity.name,
      }); // Debug log

      console.log("Received args:", args); // Debug log

      // Validate inputs using new validation helpers
      assertValidName(args.name);
      if (args.phone) {
        assertValidPhone(args.phone);
      }

      const existingUser = await ctx.db
        .query("user_profiles")
        .withIndex("by_clerk_user_id", (q: any) =>
          q.eq("clerkUserId", identity.subject)
        )
        .unique();

      if (existingUser) {
        console.log("User already exists:", existingUser._id); // Debug log

        // Update user information in case it has changed
        const updateData: any = { updatedAt: Date.now() };
        let needsUpdate = false;

        // Update name if it's different or was empty
        if (args.name.bn &&
            (!existingUser.identity.name.bn ||
             existingUser.identity.name.bn !== args.name.bn)) {
          updateData.identity = {
            ...existingUser.identity,
            name: args.name
          };
          needsUpdate = true;
        }

        // Update phone if provided and different
        if (args.phone !== undefined &&
            existingUser.contact.phone !== args.phone) {
          updateData.contact = {
            ...existingUser.contact,
            phone: args.phone || ""
          };
          needsUpdate = true;
        }

        // If user wasn't onboarded but now has a phone, mark as onboarded
        if (!existingUser.isOnboarded && args.phone) {
          updateData.isOnboarded = true;
          needsUpdate = true;
        }

        if (needsUpdate) {
          console.log("Updating existing user with new information");
          await ctx.db.patch(existingUser._id, updateData);
        }

        return existingUser._id;
      }

      const stableUserId = generateStableId();
      const now = Date.now();

      // Check if this is the first user (make them admin)
      const existingUsers = await ctx.db.query("user_profiles").collect();
      const isFirstUser = existingUsers.length === 0;

      console.log("Inserting new user with clerkUserId:", identity.subject); // Debug log

      const userId = await ctx.db.insert("user_profiles", {
        clerkUserId: identity.subject, // This should now be properly set
        stableUserId,
        identity: {
          name: args.name,
        },
        contact: {
          phone: args.phone || "",
        },
        preferences: {
          language: "bn",
          theme: "auto",
        },
        role: isFirstUser ? "admin" : "user", // First user becomes admin
        isOnboarded: !!args.phone,
        createdAt: now,
        updatedAt: now,
      });

      await ctx.db.insert("audit_logs", {
        userId: userId,
        action: "user:created",
        targetId: userId,
        details: {
          source: "sign_up",
          clerkUserId: identity.subject, // Store for debugging
        },
        createdAt: now,
      });

      console.log("User created successfully with ID:", userId); // Debug log
      return userId;
    } catch (error: any) {
      console.error("Error creating user:", error);
      // Re-throw known validation errors
      if (error instanceof ValidationError) {
        throw error;
      }
      throw new Error("Failed to create user profile: " + (error.message || error));
    }
  },
});

// 4. Update User Profile (with comprehensive validation)
export const update = mutation({
  args: {
    updates: v.object({
      name: v.optional(v.object({
        bn: v.string(),
        en: v.optional(v.string()),
      })),
      phone: v.optional(v.string()),
      preferences: v.optional(v.object({
        language: v.union(v.literal("bn"), v.literal("en")),
        theme: v.union(v.literal("light"), v.literal("dark"), v.literal("auto")),
      })),
    }),
  },
  handler: async (ctx, args) => {
    try {
      const user = await getCurrentUser(ctx);

      // Validate updates using new validation helpers
      const validationResult = validateUserUpdatesNew(args.updates);
      if (!validationResult.isValid) {
        throw new ValidationError(validationResult.errors.join(", "));
      }

      const updateData: any = { updatedAt: Date.now() };
      let becameOnboarded = false;

      if (args.updates.name) {
        updateData.identity = { ...user.identity, name: args.updates.name };
      }

      if (args.updates.phone !== undefined) {
        updateData.contact = { ...user.contact, phone: args.updates.phone };
        if (args.updates.phone && !user.contact.phone) {
          becameOnboarded = true;
        }
      }

      if (args.updates.preferences) {
        updateData.preferences = args.updates.preferences;
      }

      if (becameOnboarded && !user.isOnboarded) {
        updateData.isOnboarded = true;
      }

      await ctx.db.patch(user._id, updateData);

      await ctx.db.insert("audit_logs", {
        userId: user._id,
        action: "user:updated",
        targetId: user._id,
        details: {
          updatedFields: Object.keys(args.updates),
        },
        createdAt: Date.now(),
      });

      return { success: true };
    } catch (error) {
      console.error("Error updating user:", error);
      if (error instanceof ValidationError) {
        throw error;
      }
      throw new Error("Failed to update user profile");
    }
  },
});

// 5. Promote User to Admin (Admin only)
export const promoteToAdmin = mutation({
  args: {
    userId: v.id("user_profiles"),
  },
  handler: async (ctx, args) => {
    try {
      // Use the new admin helper
      const { user: currentUser } = await requireAdmin(ctx);

      console.log("promoteToAdmin called by admin:", currentUser._id);

      // Get the user to promote
      const userToPromote = await ctx.db.get(args.userId);
      if (!userToPromote) {
        throw new NotFoundError("User");
      }

      // Update the user's role
      await ctx.db.patch(args.userId, {
        role: "admin",
        updatedAt: Date.now(),
      });

      // Log the action
      await ctx.db.insert("audit_logs", {
        userId: currentUser._id,
        action: "user:promoted_to_admin",
        targetId: args.userId,
        details: {
          promotedUserId: args.userId,
          previousRole: userToPromote.role,
        },
        createdAt: Date.now(),
      });

      return { success: true };
    } catch (error) {
      console.error("Error promoting user to admin:", error);
      if (error instanceof ValidationError || error instanceof NotFoundError) {
        throw error;
      }
      throw new Error("Failed to promote user to admin");
    }
  },
});

// 6. List Users (Admin only with pagination)
export const list = query({
  args: {
    isOnboarded: v.optional(v.boolean()),
    limit: v.optional(v.number()),
    cursor: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    try {
      // Use the new admin helper
      await requireAdmin(ctx);

      console.log("list users called with args:", args);

      let query = ctx.db.query("user_profiles");

      if (args.isOnboarded !== undefined) {
        query = query.filter((q: any) => q.eq(q.field("isOnboarded"), args.isOnboarded));
      }

      const users = await query
        .order("desc")
        .take(args.limit || 50);

      return {
        users,
        nextCursor: users.length === (args.limit || 50)
          ? users[users.length - 1]._id
          : null,
      };
    } catch (error) {
      console.error("Error listing users:", error);
      if (error instanceof ValidationError || error instanceof NotFoundError) {
        throw error;
      }
      throw new Error("Failed to list users");
    }
  },
});

// Helper functions
function generateStableId(): string {
  return `usr_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}