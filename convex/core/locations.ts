// convex/locations.ts
import { query, mutation } from "../_generated/server";
import { v } from "convex/values";

// 1. Get Location by ID (public access)
export const get = query({
  args: {
    locationId: v.id("locations")
  },
  handler: async (ctx, args) => {
    // No auth required for public location data
    return await ctx.db.get(args.locationId);
  },
});

// 2. List Locations by Parent (with pagination)
export const listByParent = query({
  args: {
    parentId: v.optional(v.id("locations")),
    type: v.optional(v.union(
      v.literal("division"),
      v.literal("district"),
      v.literal("upazila")
    )),
    isActive: v.optional(v.boolean()),
    limit: v.optional(v.number()),
    cursor: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    let query: any = ctx.db.query("locations");

    // Use index for efficient filtering
    if (args.parentId) {
      query = query.withIndex("by_parent", (q: any) => 
        q.eq("parentId", args.parentId)
      );
    } else {
      // Root locations (no parent)
      query = query.filter((q: any) => q.eq(q.field("parentId"), undefined));
    }

    // Additional filters
    if (args.type) {
      query = query.filter((q: any) => q.eq(q.field("type"), args.type));
    }

    if (args.isActive !== undefined) {
      query = query.filter((q: any) => q.eq(q.field("isActive"), args.isActive));
    }

    // Additional filters
    if (args.type) {
      query = query.filter((q: any) => q.eq(q.field("type"), args.type));
    }

    if (args.isActive !== undefined) {
      query = query.filter((q: any) => q.eq(q.field("isActive"), args.isActive));
    }

    // Order and limit results
    const results = await query
      .order("asc")
      .take(args.limit || 50);

    return {
      results,
      nextCursor: results.length === (args.limit || 50) 
        ? results[results.length - 1]._id 
        : undefined
    };
  },
});

// 3. Create Location (admin only)
export const create = mutation({
  args: {
    name: v.object({
      bn: v.string(),
      en: v.string()
    }),
    slug: v.string(),
    type: v.union(
      v.literal("division"),
      v.literal("district"),
      v.literal("upazila")
    ),
    parentId: v.optional(v.id("locations")),
    isActive: v.boolean()
  },
  handler: async (ctx, args) => {
    // Admin authorization check
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Unauthorized");
    }

    // Check if user has admin role
    const user = await ctx.db
      .query("user_profiles")
      .withIndex("by_clerk_user_id", q => 
        q.eq("clerkUserId", identity.subject)
      )
      .unique();

    if (!user || user.role !== "admin") {
      throw new Error("Admin access required");
    }

    // Check for duplicate slug
    const existing = await ctx.db
      .query("locations")
      .withIndex("by_slug", (q: any) => q.eq("slug", args.slug))
      .unique();

    if (existing) {
      throw new Error("Location with this slug already exists");
    }

    // Validate parent relationship
    if (args.parentId) {
      const parent = await ctx.db.get(args.parentId);
      if (!parent) {
        throw new Error("Parent location not found");
      }

      // Validate hierarchy: upazila -> district -> division
      const validHierarchy: Record<string, string[]> = {
        upazila: ["district"],
        district: ["division"],
        division: []
      };

      if (!validHierarchy[args.type]?.includes(parent.type)) {
        throw new Error(`Invalid parent type for ${args.type}`);
      }
    }

    const now = Date.now();
    
    const locationId = await ctx.db.insert("locations", {
      ...args,
      createdAt: now
    });

    // Log the action
    await ctx.db.insert("audit_logs", {
      userId: user._id,
      action: "location:created",
      targetId: locationId,
      details: {
        name: args.name,
        type: args.type,
        slug: args.slug
      },
      createdAt: now
    });

    return locationId;
  },
});

// 4. Update Location (admin only)
export const update = mutation({
  args: {
    locationId: v.id("locations"),
    updates: v.object({
      name: v.optional(v.object({
        bn: v.string(),
        en: v.string()
      })),
      slug: v.optional(v.string()),
      isActive: v.optional(v.boolean())
    })
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Unauthorized");
    }

    const user = await ctx.db
      .query("user_profiles")
      .withIndex("by_clerk_user_id", q => 
        q.eq("clerkUserId", identity.subject)
      )
      .unique();

    if (!user || user.role !== "admin") {
      throw new Error("Admin access required");
    }

    // Check for duplicate slug if slug is being updated
    if (args.updates.slug) {
      const existing = await ctx.db
        .query("locations")
        .withIndex("by_slug", (q: any) => q.eq("slug", args.updates.slug!))
        .filter((q: any) => q.neq(q.field("_id"), args.locationId))
        .unique();

      if (existing) {
        throw new Error("Location with this slug already exists");
      }
    }

    const now = Date.now();
    
    await ctx.db.patch(args.locationId, {
      ...args.updates,
      createdAt: now // Note: This should probably be renamed to updatedAt in schema
    });

    // Log the action
    await ctx.db.insert("audit_logs", {
      userId: user._id,
      action: "location:updated",
      targetId: args.locationId,
      details: {
        updates: args.updates
      },
      createdAt: now
    });

    return { success: true };
  },
});

// 5. Get Location Tree (Hierarchical data)
export const getTree = query({
  args: {
    rootId: v.optional(v.id("locations")),
    maxDepth: v.optional(v.number())
  },
  handler: async (ctx, args) => {
    const buildTree = async (locationId: any, depth: number = 0): Promise<any> => {
      if (args.maxDepth && depth >= args.maxDepth) {
        return null;
      }

      let query: any = ctx.db.query("locations");

      if (locationId) {
        query = query.withIndex("by_parent", (q: any) => q.eq("parentId", locationId));
      } else {
        // Root locations (no parent)
        query = query.filter((q: any) => q.eq(q.field("parentId"), undefined));
      }

      const locations = await query
        .filter((q: any) => q.eq(q.field("isActive"), true))
        .collect();

      const results = await Promise.all(
        locations.map(async (location: any) => ({
          ...location,
          children: await buildTree(location._id, depth + 1)
        }))
      );

      return results;
    };

    return await buildTree(args.rootId);
  },
});

// 6. Search Locations (with name search)
export const search = query({
  args: {
    query: v.string(),
    type: v.optional(v.union(
      v.literal("division"),
      v.literal("district"),
      v.literal("upazila")
    )),
    limit: v.optional(v.number())
  },
  handler: async (ctx, args) => {
    // Get all active locations of specified type
    let query: any = ctx.db.query("locations")
      .filter((q: any) => q.eq(q.field("isActive"), true));
    
    if (args.type) {
      query = query.filter((q: any) => q.eq(q.field("type"), args.type));
    }

    const allLocations = await query.collect();

    const searchTerm = args.query.toLowerCase();
    
    return allLocations.filter((location: any) =>
      location.name.bn.toLowerCase().includes(searchTerm) ||
      location.name.en.toLowerCase().includes(searchTerm) ||
      location.slug.toLowerCase().includes(searchTerm)
    ).slice(0, args.limit || 20);
  },
});

// 7. List all locations of a specific type
export const listByType = query({
  args: {
    type: v.union(
      v.literal("division"),
      v.literal("district"),
      v.literal("upazila")
    ),
    isActive: v.optional(v.boolean()),
    limit: v.optional(v.number())
  },
  handler: async (ctx, args) => {
    let query: any = ctx.db.query("locations")
      .filter((q: any) => q.eq(q.field("type"), args.type));

    if (args.isActive !== undefined) {
      query = query.filter((q: any) => q.eq(q.field("isActive"), args.isActive));
    }

    const results = await query
      .order("asc")
      .take(args.limit || 100);

    return results;
  },
});