// src/components/ListingCard.tsx
import { useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";

interface ListingCardProps {
  listing: any;
  compact?: boolean;
}

export function ListingCard({ listing, compact = false }: ListingCardProps) {
  const recordClick = useMutation(api.directory.interactions.recordClick);

  const handleContactClick = async () => {
    await recordClick({ listingId: listing._id });
    // In a real app, you would show the phone number or contact form here
    alert(`Contact: ${listing.contact.phone}`);
  };

  if (compact) {
    return (
      <div className="listing-card bg-white rounded-lg shadow p-4 hover:shadow-md transition-shadow">
        <h3 className="font-semibold text-lg mb-1">{listing.name.bn}</h3>
        {listing.name.en && (
          <p className="text-gray-600 text-sm mb-2">{listing.name.en}</p>
        )}
        <button
          onClick={() => void handleContactClick()}
          className="text-blue-500 hover:text-blue-700 text-sm font-medium"
        >
          📞 Contact
        </button>
      </div>
    );
  }

  return (
    <div className="listing-card bg-white rounded-lg shadow overflow-hidden hover:shadow-md transition-shadow">
      <div className="p-4">
        <div className="flex justify-between items-start">
          <div>
            <h3 className="font-semibold text-xl mb-1">{listing.name.bn}</h3>
            {listing.name.en && (
              <p className="text-gray-600 mb-2">{listing.name.en}</p>
            )}
          </div>
          {listing.isFeatured && (
            <span className="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded">
              Featured
            </span>
          )}
        </div>

        <div className="flex items-center text-sm text-gray-500 mb-3">
          <span className="mr-3">📍 {listing.location?.name?.bn}</span>
          <span>📁 {listing.category?.name?.bn}</span>
        </div>

        <div className="flex justify-between items-center">
          <div className="text-sm text-gray-500">
            <span className="mr-3">👁️ {listing.viewCount || 0}</span>
            <span>👆 {listing.clickCount || 0}</span>
          </div>
          <button
            onClick={() => void handleContactClick()}
            className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm"
          >
            📞 Contact
          </button>
        </div>
      </div>
    </div>
  );
}
