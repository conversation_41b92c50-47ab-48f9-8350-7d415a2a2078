// src/core/AdminPanel.tsx
import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { useState } from "react";

export function AdminPanel() {
  const [rejectionReason, setRejectionReason] = useState("");
  const [approvalNotes, setApprovalNotes] = useState("");
  const [selectedListing, setSelectedListing] = useState<any>(null);

  const { results: pendingListings, isLoading } = useQuery(api.admin.getPendingListings, {});
  const approve = useMutation(api.admin.approveListing);
  const reject = useMutation(api.admin.rejectListing);
  const stats = useQuery(api.admin.getDashboardStats);

  const handleApprove = async (listingId: Id<"listings">) => {
    try {
      await approve({ listingId, notes: approvalNotes });
      setApprovalNotes(""); // Reset form
      setSelectedListing(null);
    } catch (error: any) {
      alert(`Approval failed: ${error.message}`);
    }
  };

  const handleReject = async (listingId: Id<"listings">) => {
    if (!rejectionReason.trim()) {
      alert("Please provide a rejection reason");
      return;
    }
    
    try {
      await reject({ listingId, reason: rejectionReason });
      setRejectionReason(""); // Reset form
      setSelectedListing(null);
    } catch (error: any) {
      alert(`Rejection failed: ${error.message}`);
    }
  };

  if (isLoading) return <div>Loading admin panel...</div>;

  return (
    <div className="admin-panel p-6">
      <h1 className="text-2xl font-bold mb-6">Admin Dashboard</h1>
      
      {stats && (
        <div className="stats-grid grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-blue-100 p-4 rounded-lg">
            <div className="text-sm text-blue-800">Total</div>
            <div className="text-2xl font-bold">{stats.total}</div>
          </div>
          <div className="bg-green-100 p-4 rounded-lg">
            <div className="text-sm text-green-800">Published</div>
            <div className="text-2xl font-bold">{stats.published}</div>
          </div>
          <div className="bg-yellow-100 p-4 rounded-lg">
            <div className="text-sm text-yellow-800">Pending</div>
            <div className="text-2xl font-bold">{stats.pending}</div>
          </div>
          <div className="bg-red-100 p-4 rounded-lg">
            <div className="text-sm text-red-800">Rejected</div>
            <div className="text-2xl font-bold">{stats.rejected}</div>
          </div>
        </div>
      )}

      <h2 className="text-xl font-semibold mb-4">Pending Review ({pendingListings?.length || 0})</h2>
      
      {!pendingListings?.length ? (
        <p className="text-gray-500">No listings pending review! 🎉</p>
      ) : (
        <div className="listings-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {pendingListings.map((listing: any) => (
            <div key={listing._id} className="listing-card border rounded-lg p-4 shadow-sm">
              <h3 className="text-lg font-semibold">{listing.name.bn}</h3>
              <p className="text-gray-600">📞 {listing.contact.phone}</p>
              
              <div className="action-buttons flex gap-2 mt-4">
                <button 
                  onClick={() => void handleApprove(listing._id)}
                  className="btn-success bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded"
                >
                  ✅ Approve
                </button>
                
                <button 
                  onClick={() => setSelectedListing(listing)}
                  className="btn-danger bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded"
                >
                  ❌ Reject
                </button>
              </div>

              {selectedListing?._id === listing._id && (
                <div className="rejection-form mt-4 p-3 border rounded bg-gray-50">
                  <textarea
                    placeholder="Reason for rejection (required)"
                    value={rejectionReason}
                    onChange={(e) => setRejectionReason(e.target.value)}
                    className="w-full p-2 border rounded mb-2"
                    rows={3}
                  />
                  <div className="flex gap-2">
                    <button 
                  onClick={() => void handleReject(listing._id)}
                  className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded"
                >
                      Confirm Reject
                    </button>
                    <button 
                      onClick={() => setSelectedListing(null)}
                      className="bg-gray-300 hover:bg-gray-400 px-3 py-1 rounded"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}