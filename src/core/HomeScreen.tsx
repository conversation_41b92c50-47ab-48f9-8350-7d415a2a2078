// src/core/HomeScreen.tsx
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { AdminPanel } from "./AdminPanel";
import { UserManagementPanel } from "./UserManagementPanel";
import { DirectorySearchPage } from "../features/directory";
import { useState } from "react";

export default function HomeScreen() {
  const userProfile = useQuery(api.users.getCurrent);
  const [activePanel, setActivePanel] = useState<
    "dashboard" | "admin" | "users" | "directory"
  >("dashboard");

  if (userProfile === undefined) {
    return <div>Loading...</div>;
  }

  if (userProfile === null) {
    return <div>User profile not found.</div>;
  }

  const isAdmin = userProfile.role === "admin";

  return (
    <div className="flex flex-col gap-4">
      <h2 className="text-2xl font-bold">Home</h2>

      <div className="flex gap-2 mb-4">
        <button
          onClick={() => setActivePanel("dashboard")}
          className={`px-4 py-2 rounded ${activePanel === "dashboard" ? "bg-blue-500 text-white" : "bg-gray-200"}`}
        >
          Dashboard
        </button>

        <button
          onClick={() => setActivePanel("directory")}
          className={`px-4 py-2 rounded ${activePanel === "directory" ? "bg-blue-500 text-white" : "bg-gray-200"}`}
        >
          Business Directory
        </button>

        {isAdmin && (
          <>
            <button
              onClick={() => setActivePanel("admin")}
              className={`px-4 py-2 rounded ${activePanel === "admin" ? "bg-blue-500 text-white" : "bg-gray-200"}`}
            >
              Listing Moderation
            </button>
            <button
              onClick={() => setActivePanel("users")}
              className={`px-4 py-2 rounded ${activePanel === "users" ? "bg-blue-500 text-white" : "bg-gray-200"}`}
            >
              User Management
            </button>
          </>
        )}
      </div>

      {activePanel === "dashboard" && (
        <div className="p-4 border rounded-md bg-gray-100 dark:bg-gray-800">
          <h3 className="text-lg font-semibold">
            Welcome,{" "}
            {userProfile.identity.name.en ||
              userProfile.identity.name.bn ||
              "User"}
            !
          </h3>
          <p>
            Your role is: <strong>{userProfile.role}</strong>
          </p>
          <p>
            Your onboarding status is:{" "}
            <strong>
              {userProfile.isOnboarded ? "Complete" : "Incomplete"}
            </strong>
          </p>
          {!userProfile.isOnboarded && (
            <p className="mt-2 text-sm text-yellow-700">
              Please complete your profile.
            </p>
          )}
        </div>
      )}

      {activePanel === "directory" && <DirectorySearchPage />}

      {activePanel === "admin" && isAdmin && <AdminPanel />}
      {activePanel === "users" && isAdmin && <UserManagementPanel />}
    </div>
  );
}
