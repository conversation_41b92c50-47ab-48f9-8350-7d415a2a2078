// src/core/UserManagementPanel.tsx
import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { useState } from "react";

// Define the user type based on your Convex schema
type UserProfile = {
  _id: Id<"user_profiles">;
  identity: {
    name: {
      bn: string;
      en?: string;
    };
  };
  contact: {
    phone?: string;
  };
  role: "admin" | "business_owner" | "user";
  isOnboarded: boolean;
};

export function UserManagementPanel() {
  const [selectedUser, setSelectedUser] = useState<UserProfile | null>(null);
  const [isPromoting, setIsPromoting] = useState(false);
  const usersQuery = useQuery(api.core.users.list, {});
  const promoteToAdmin = useMutation(api.core.users.promoteToAdmin);

  const handlePromoteToAdmin = async (userId: Id<"user_profiles">) => {
    setIsPromoting(true);
    try {
      await promoteToAdmin({ userId });
      alert("User promoted to admin successfully!");
      setSelectedUser(null);
    } catch (error: any) {
      alert(`Failed to promote user: ${error.message}`);
    } finally {
      setIsPromoting(false);
    }
  };

  if (usersQuery === undefined) return <div>Loading users...</div>;
  
  if (!usersQuery) return <div>No users found.</div>;
  const { users } = usersQuery;

  return (
    <div className="user-management-panel p-6">
      <h1 className="text-2xl font-bold mb-6">User Management</h1>

      <div className="users-list">
        <h2 className="text-xl font-semibold mb-4">
          All Users ({users.length})
        </h2>

        {!users.length ? (
          <p className="text-gray-500">No users found.</p>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full bg-white border border-gray-200">
              <thead>
                <tr className="bg-gray-100">
                  <th className="py-2 px-4 border-b text-left">Name</th>
                  <th className="py-2 px-4 border-b text-left">Phone</th>
                  <th className="py-2 px-4 border-b text-left">Role</th>
                  <th className="py-2 px-4 border-b text-left">Onboarded</th>
                  <th className="py-2 px-4 border-b text-left">Actions</th>
                </tr>
              </thead>
              <tbody>
                {users.map((user: UserProfile) => (
                  <tr key={user._id} className="hover:bg-gray-50">
                    <td className="py-2 px-4 border-b">
                      {user.identity.name.en || user.identity.name.bn}
                    </td>
                    <td className="py-2 px-4 border-b">
                      {user.contact.phone || "N/A"}
                    </td>
                    <td className="py-2 px-4 border-b">
                      <span
                        className={`px-2 py-1 rounded text-xs ${
                          user.role === "admin"
                            ? "bg-red-100 text-red-800"
                            : user.role === "business_owner"
                              ? "bg-blue-100 text-blue-800"
                              : "bg-gray-100 text-gray-800"
                        }`}
                      >
                        {user.role}
                      </span>
                    </td>
                    <td className="py-2 px-4 border-b">
                      {user.isOnboarded ? "Yes" : "No"}
                    </td>
                    <td className="py-2 px-4 border-b">
                      {user.role !== "admin" && (
                        <button
                          onClick={() => setSelectedUser(user)}
                          className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm disabled:opacity-50"
                          disabled={isPromoting}
                        >
                          Make Admin
                        </button>
                      )}

                      {selectedUser?._id === user._id && (
                        <div className="mt-2 p-3 border rounded bg-gray-50">
                          <p className="mb-2">
                            Are you sure you want to promote{" "}
                            {user.identity.name.bn} to admin?
                          </p>
                          <div className="flex gap-2">
                            <button
                              onClick={() =>
                                void handlePromoteToAdmin(user._id)
                              }
                              className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm disabled:opacity-50"
                              disabled={isPromoting}
                            >
                              {isPromoting ? "Promoting..." : "Confirm"}
                            </button>
                            <button
                              onClick={() => setSelectedUser(null)}
                              className="bg-gray-300 hover:bg-gray-400 px-3 py-1 rounded text-sm"
                              disabled={isPromoting}
                            >
                              Cancel
                            </button>
                          </div>
                        </div>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}
