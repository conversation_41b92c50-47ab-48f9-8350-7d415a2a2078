// src/features/directory/DirectoryListingDetail.tsx
import { useQuery, useMutation } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { DirectoryListingCard } from "./DirectoryListingCard";

interface DirectoryListingDetailProps {
  listingId: string;
}

export function DirectoryListingDetail({
  listingId,
}: DirectoryListingDetailProps) {
  const listing = useQuery(api.directory.search.getListingDetail, {
    listingId,
  });
  const similarListings = useQuery(
    api.directory.interactions.getSimilarListings,
    {
      listingId,
      limit: 4,
    },
  );
  const recordClick = useMutation(api.directory.interactions.recordClick);
  const [showPhone, setShowPhone] = useState(false);

  const handleContactClick = async () => {
    await recordClick({ listingId });
    setShowPhone(true);
  };

  if (!listing) return <div className="p-6">Loading...</div>;

  return (
    <div className="directory-listing-detail max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow p-6 mb-6">
        <div className="flex justify-between items-start mb-4">
          <div>
            <h1 className="text-3xl font-bold mb-2">{listing.name.bn}</h1>
            {listing.name.en && (
              <h2 className="text-xl text-gray-600 mb-4">{listing.name.en}</h2>
            )}
          </div>
          {listing.isFeatured && (
            <span className="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm">
              Featured Business
            </span>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <h3 className="font-semibold text-lg mb-2">Location</h3>
            <p className="text-gray-700">📍 {listing.location?.name?.bn}</p>
            {listing.location?.name?.en && (
              <p className="text-gray-600">{listing.location?.name?.en}</p>
            )}
          </div>

          <div>
            <h3 className="font-semibold text-lg mb-2">Category</h3>
            <p className="text-gray-700">📁 {listing.category?.name?.bn}</p>
            {listing.category?.name?.en && (
              <p className="text-gray-600">{listing.category?.name?.en}</p>
            )}
          </div>
        </div>

        <div className="flex flex-wrap gap-4 mb-6">
          <div className="bg-gray-100 px-4 py-2 rounded">
            <span className="text-gray-600">
              👁️ Views: {listing.viewCount || 0}
            </span>
          </div>
          <div className="bg-gray-100 px-4 py-2 rounded">
            <span className="text-gray-600">
              👆 Contacts: {listing.clickCount || 0}
            </span>
          </div>
        </div>

        <div className="contact-section">
          {showPhone ? (
            <div className="bg-green-100 text-green-800 px-6 py-3 rounded-lg text-lg font-semibold text-center">
              📞 {listing.contact.phone}
            </div>
          ) : (
            <button
              onClick={() => void handleContactClick()}
              className="bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg text-lg font-semibold w-full"
            >
              📞 Show Phone Number
            </button>
          )}
        </div>
      </div>

      {similarListings && similarListings.length > 0 && (
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-xl font-semibold mb-4">Similar Businesses</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {similarListings.map((similar: any) => (
              <DirectoryListingCard key={similar._id} listingId={similar._id} />
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
