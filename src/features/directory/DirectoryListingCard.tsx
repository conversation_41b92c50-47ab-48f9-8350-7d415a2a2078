// src/features/directory/DirectoryListingCard.tsx
import { useQuery, useMutation } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { useState } from "react";

interface DirectoryListingCardProps {
  listingId: string;
}

export function DirectoryListingCard({ listingId }: DirectoryListingCardProps) {
  const listing = useQuery(api.directory.search.getListingDetail, {
    listingId,
  });
  const recordClick = useMutation(api.directory.interactions.recordClick);
  const [showPhone, setShowPhone] = useState(false);

  const handleContactClick = async () => {
    await recordClick({ listingId });
    setShowPhone(true);
  };

  if (!listing)
    return <div className="bg-white rounded-lg shadow p-4">Loading...</div>;

  return (
    <div className="listing-card bg-white rounded-lg shadow overflow-hidden hover:shadow-md transition-shadow">
      <div className="p-4">
        <div className="flex justify-between items-start">
          <div>
            <h3 className="font-semibold text-xl mb-1">{listing.name.bn}</h3>
            {listing.name.en && (
              <p className="text-gray-600 mb-2">{listing.name.en}</p>
            )}
          </div>
          {listing.isFeatured && (
            <span className="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded">
              Featured
            </span>
          )}
        </div>

        <div className="flex items-center text-sm text-gray-500 mb-3">
          <span className="mr-3">📍 {listing.location?.name?.bn}</span>
          <span>📁 {listing.category?.name?.bn}</span>
        </div>

        <div className="flex justify-between items-center">
          <div className="text-sm text-gray-500">
            <span className="mr-3">👁️ {listing.viewCount || 0}</span>
            <span>👆 {listing.clickCount || 0}</span>
          </div>
          {showPhone ? (
            <div className="bg-green-100 text-green-800 px-3 py-1 rounded">
              📞 {listing.contact.phone}
            </div>
          ) : (
            <button
              onClick={() => void handleContactClick()}
              className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm"
            >
              📞 Contact
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
