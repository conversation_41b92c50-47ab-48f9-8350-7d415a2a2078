// src/App.tsx
"use client";

import { Authenticated, Unauthenticated, useMutation } from "convex/react";
import { SignInButton, UserButton, useUser, useClerk } from "@clerk/clerk-react";
import { useEffect } from "react";
// Import the correct API definition generated by Convex
import { api } from "../convex/_generated/api";
import { HomeScreen } from "./core/HomeScreen.tsx"; // We will create this next

export default function App() {
  return (
    <>
      <header className="sticky top-0 z-10 bg-white dark:bg-gray-900 p-4 border-b-2 border-gray-200 dark:border-gray-800 flex flex-row justify-between items-center">
        <h1 className="text-lg font-bold">Smart Village Superapp</h1>
        <UserButton />
      </header>
      <main className="p-8">
        <Unauthenticated>
          <SignInView />
        </Unauthenticated>
        <Authenticated>
          <AuthenticatedApp />
        </Authenticated>
      </main>
    </>
  );
}

function AuthenticatedApp() {
  const { user, isLoaded } = useUser();
  const createOnSignUp = useMutation(api.users.createOnSignUp);

  useEffect(() => {
    // Only create user if we have authentication info and it's loaded
    if (isLoaded && user) {
      console.log("User authenticated:", user.id); // Debug log
      
      // Determine user name with better fallbacks
      const userName = user.fullName || 
                      (user.firstName && user.lastName ? `${user.firstName} ${user.lastName}` : null) ||
                      user.emailAddresses?.[0]?.emailAddress ||
                      "নতুন ব্যবহারকারী"; // "New User" in Bengali
      
      console.log("Creating user with name:", userName); // Debug log
      
      // This mutation is idempotent, so it's safe to call on every login.
      // We pass more meaningful initial values for better user experience.
      void createOnSignUp({ 
        name: { 
          bn: userName,
          en: userName 
        }, 
        phone: undefined // Allow user to add phone during onboarding
      });
    }
  }, [user, isLoaded, createOnSignUp]);

  return <HomeScreen />;
}

function SignInView() {
  const { isLoaded } = useUser();
  const { signOut } = useClerk();
  
  // Don't render anything while checking authentication state
  if (!isLoaded) {
    return <div>Loading...</div>;
  }
  
  return (
    <div className="flex flex-col items-center gap-4">
      <h2 className="text-2xl font-bold">Welcome</h2>
      <p>Please sign in to continue</p>
      <div className="flex gap-2">
        <SignInButton mode="modal">
          <button className="bg-gray-800 text-white text-sm px-4 py-2 rounded-md border-2">
            Sign in
          </button>
        </SignInButton>
        <button 
          onClick={() => void signOut()}
          className="bg-red-600 text-white text-sm px-4 py-2 rounded-md border-2"
        >
          Sign Out (if stuck)
        </button>
      </div>
    </div>
  );
}