{"name": "core-super", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "npm-run-all --parallel dev:frontend dev:backend", "dev:frontend": "vite --open", "dev:backend": "convex dev", "predev": "convex dev --until-success && convex dashboard", "build": "tsc -b && vite build", "lint": "tsc && eslint .  --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@clerk/clerk-react": "^5.25.0", "convex": "^1.27.0", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/js": "^9.21.0", "@tailwindcss/vite": "^4.0.14", "@types/node": "^22.13.10", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "npm-run-all": "^4.1.5", "prettier": "^3.5.3", "tailwindcss": "^4.0.14", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}